/**
 * TEAM CARD COMPONENT
 * 
 * Creates and manages team card displays for the teams page.
 * Shows team information, players, and records.
 */

/**
 * Create a team card element
 * @param {Object} team - Team data object
 * @param {boolean} showPlayers - Whether to show player list
 * @returns {HTMLElement} - Team card element
 */
function createTeamCard(team, showPlayers = true) {
    if (!team) {
        console.warn('Team data is missing');
        return createPlaceholderTeamCard();
    }

    const card = document.createElement('div');
    card.className = `team-card ${team.status}`;
    card.setAttribute('data-team-id', team.id);

    const recordString = `${team.record.wins}-${team.record.losses}`;
    const statusClass = team.status === 'active' ? 'active' : 'eliminated';
    const statusText = team.status === 'active' ? 'ACTIVE' : 'ELIMINATED';

    card.innerHTML = `
        <div class="team-card-header">
            <div class="team-card-logo">${team.logo}</div>
            <div class="team-card-info">
                <h3 class="team-card-name">${team.name}</h3>
                <div class="team-card-record">Record: ${recordString}</div>
                <div class="team-status ${statusClass}">${statusText}</div>
            </div>
        </div>
        
        ${showPlayers ? createPlayersListHTML(team.players) : ''}
        
        <div class="team-card-footer">
            <button class="team-details-btn" onclick="showTeamDetails('${team.id}')">
                View Details
            </button>
        </div>
    `;

    return card;
}

/**
 * Create players list HTML
 * @param {Array} players - Array of player objects
 * @returns {string} - HTML string for players list
 */
function createPlayersListHTML(players) {
    if (!players || players.length === 0) {
        return '<div class="no-players">No players listed</div>';
    }

    const playersHTML = players.map(player => `
        <div class="player">
            <span class="player-name">${player.name}</span>
            <span class="player-role" style="color: ${roleColors[player.role] || '#AAABAD'}">${player.role}</span>
        </div>
    `).join('');

    return `
        <div class="team-players">
            <h4>Players:</h4>
            <div class="players-list">
                ${playersHTML}
            </div>
        </div>
    `;
}

/**
 * Create a compact team card for smaller displays
 * @param {Object} team - Team data object
 * @returns {HTMLElement} - Compact team card element
 */
function createCompactTeamCard(team) {
    if (!team) {
        return createPlaceholderTeamCard();
    }

    const card = document.createElement('div');
    card.className = `team-card compact ${team.status}`;
    card.setAttribute('data-team-id', team.id);

    const recordString = `${team.record.wins}-${team.record.losses}`;

    card.innerHTML = `
        <div class="team-compact-content">
            <div class="team-card-logo small">${team.logo}</div>
            <div class="team-compact-info">
                <div class="team-card-name">${team.name}</div>
                <div class="team-card-record">${recordString}</div>
            </div>
            <div class="team-status ${team.status}">${team.status.toUpperCase()}</div>
        </div>
    `;

    return card;
}

/**
 * Create a placeholder team card for missing data
 * @returns {HTMLElement} - Placeholder team card element
 */
function createPlaceholderTeamCard() {
    const card = document.createElement('div');
    card.className = 'team-card placeholder';
    card.innerHTML = `
        <div class="team-card-header">
            <div class="team-card-logo">?</div>
            <div class="team-card-info">
                <h3 class="team-card-name">TBD</h3>
                <div class="team-card-record">Record: 0-0</div>
                <div class="team-status">TBD</div>
            </div>
        </div>
        
        <div class="team-players">
            <h4>Players:</h4>
            <div class="no-players">Players TBD</div>
        </div>
    `;
    
    return card;
}

/**
 * Render multiple team cards to a container
 * @param {Array} teams - Array of team objects
 * @param {HTMLElement} container - Container element to render cards into
 * @param {boolean} showPlayers - Whether to show player lists
 * @param {boolean} compact - Whether to use compact card style
 */
function renderTeamCards(teams, container, showPlayers = true, compact = false) {
    if (!container) {
        console.error('Container element not found');
        return;
    }

    // Clear existing content
    container.innerHTML = '';

    if (!teams || teams.length === 0) {
        container.innerHTML = `
            <div class="no-teams">
                <p>No teams to display</p>
            </div>
        `;
        return;
    }

    // Create and append team cards
    teams.forEach(team => {
        const card = compact ? 
            createCompactTeamCard(team) : 
            createTeamCard(team, showPlayers);
        container.appendChild(card);
    });
}

/**
 * Filter teams by status and render
 * @param {string} status - Team status to filter by ('active', 'eliminated', 'all')
 * @param {HTMLElement} container - Container to render filtered teams
 * @param {boolean} showPlayers - Whether to show player lists
 */
function renderFilteredTeams(status, container, showPlayers = true) {
    let filteredTeams;
    
    switch (status) {
        case 'active':
            filteredTeams = getActiveTeams();
            break;
        case 'eliminated':
            filteredTeams = getEliminatedTeams();
            break;
        case 'all':
        default:
            filteredTeams = teams;
            break;
    }

    renderTeamCards(filteredTeams, container, showPlayers);
}

/**
 * Show detailed team information
 * @param {string} teamId - Team ID to show details for
 */
function showTeamDetails(teamId) {
    const team = getTeamById(teamId);
    if (!team) {
        console.error('Team not found:', teamId);
        return;
    }

    // For now, just log the team details
    // In a more complex app, this could open a modal or navigate to a details page
    console.log('Team details:', team);
    
    // You could implement a modal here:
    // openTeamModal(team);
}

/**
 * Add team filter functionality
 * @param {HTMLElement} filterContainer - Container with filter buttons
 * @param {HTMLElement} teamsContainer - Container with team cards
 */
function addTeamFilters(filterContainer, teamsContainer) {
    if (!filterContainer || !teamsContainer) return;

    const filterButtons = filterContainer.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Get filter value and apply
            const filterValue = this.getAttribute('data-filter');
            renderFilteredTeams(filterValue, teamsContainer);
        });
    });
}

/**
 * Sort teams by different criteria
 * @param {Array} teams - Array of team objects
 * @param {string} sortBy - Sort criteria ('name', 'record', 'status')
 * @param {string} order - Sort order ('asc', 'desc')
 * @returns {Array} - Sorted teams array
 */
function sortTeams(teams, sortBy = 'name', order = 'asc') {
    const sortedTeams = [...teams];
    
    sortedTeams.sort((a, b) => {
        let comparison = 0;
        
        switch (sortBy) {
            case 'name':
                comparison = a.name.localeCompare(b.name);
                break;
            case 'record':
                const aWinRate = a.record.wins / (a.record.wins + a.record.losses) || 0;
                const bWinRate = b.record.wins / (b.record.wins + b.record.losses) || 0;
                comparison = aWinRate - bWinRate;
                break;
            case 'status':
                comparison = a.status.localeCompare(b.status);
                break;
            default:
                comparison = 0;
        }
        
        return order === 'desc' ? -comparison : comparison;
    });
    
    return sortedTeams;
}

// Export functions for use in other scripts
window.TeamCardComponent = {
    createTeamCard,
    createCompactTeamCard,
    renderTeamCards,
    renderFilteredTeams,
    showTeamDetails,
    addTeamFilters,
    sortTeams
};
