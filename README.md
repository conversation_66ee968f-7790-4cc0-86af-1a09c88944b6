# Community Valorant Tournament Website

A static website for hosting and displaying community Valorant tournaments. Built with vanilla HTML, CSS, and JavaScript for easy deployment and manual updates.

## 🚀 Features

- **Home Page**: Upcoming matches, recent results, and tournament overview
- **Teams Page**: All participating teams with player rosters and records
- **Schedule Page**: Complete tournament schedule with filtering options
- **Bracket Page**: Visual tournament bracket with match progression
- **Responsive Design**: Works on desktop and mobile devices
- **Valorant-Inspired UI**: Dark theme with red accents and esports styling

## 📁 Project Structure

```
├── index.html              # Home page
├── teams.html              # Teams page
├── schedule.html           # Schedule page
├── bracket.html            # Bracket page
├── css/
│   ├── main.css           # Main styles and layout
│   └── components.css     # Component-specific styles
├── js/
│   ├── data/
│   │   ├── teams.js       # Team data and players
│   │   ├── tournaments.js # Tournament configuration
│   │   └── matches.js     # Match results and schedule
│   ├── components/
│   │   ├── navigation.js  # Navigation functionality
│   │   ├── match-card.js  # Match display components
│   │   └── team-card.js   # Team display components
│   └── pages/
│       ├── home.js        # Home page functionality
│       ├── teams.js       # Teams page functionality
│       ├── schedule.js    # Schedule page functionality
│       └── bracket.js     # Bracket page functionality
└── README.md              # This file
```

## 🛠 How to Update Tournament Data

### Adding a New Tournament

1. **Edit `js/data/tournaments.js`**:
   - Add a new tournament object to the `tournaments` array
   - Update the `currentTournament` variable
   - Configure bracket structure and rules

2. **Update `js/data/teams.js`**:
   - Add participating teams with their players
   - Make sure team IDs match those used in the tournament bracket

3. **Update `js/data/matches.js`**:
   - Add match objects corresponding to bracket matches
   - Include scheduled times and match details

### Adding Teams

Edit `js/data/teams.js`:

```javascript
{
    id: 'your-team-id',           // Unique identifier
    name: 'Your Team Name',       // Display name
    logo: 'YT',                   // 2-letter logo (or image path)
    players: [
        { name: 'PlayerName', role: 'Duelist' },
        { name: 'PlayerName', role: 'Controller' },
        { name: 'PlayerName', role: 'Initiator' },
        { name: 'PlayerName', role: 'Sentinel' },
        { name: 'PlayerName', role: 'Flex' }
    ],
    record: { wins: 0, losses: 0 },
    status: 'active'              // 'active' or 'eliminated'
}
```

### Updating Match Results

Edit `js/data/matches.js`:

1. Find the match by ID
2. Update the `score`, `winner`, and `status`
3. Add map results if available
4. Set `endTime` for completed matches

Example:
```javascript
{
    id: 'match-qf1',
    score: { team1: 2, team2: 0 },    // Best of 3 score
    winner: 'phoenix-rising',          // Winning team ID
    status: 'completed',               // 'upcoming', 'live', 'completed'
    maps: [
        {
            name: 'Ascent',
            score: { team1: 13, team2: 8 },
            winner: 'phoenix-rising'
        }
    ]
}
```

### Updating Tournament Bracket

Edit `js/data/tournaments.js`:

1. Find the bracket match in `tournament.bracket`
2. Update `winner` and `status`
3. Make sure winner advances to next round

## 🎨 Customizing the Design

### Colors

Edit CSS variables in `css/main.css`:

```css
:root {
    --primary-red: #FF4655;      /* Main accent color */
    --dark-bg: #0F1419;          /* Background color */
    --card-bg: #1E2328;          /* Card background */
    --border-color: #3C3C41;     /* Border color */
    --text-primary: #FFFFFF;     /* Primary text */
    --text-secondary: #AAABAD;   /* Secondary text */
}
```

### Fonts

The site uses 'Rajdhani' from Google Fonts. To change:

1. Update the Google Fonts link in HTML files
2. Change `font-family` in `css/main.css`

### Team Logos

Currently using text initials. To use images:

1. Add image files to an `assets/images/` folder
2. Update team `logo` property to image path
3. Modify CSS for `.team-logo` class

## 🚀 Deployment

### GitHub Pages

1. Push code to a GitHub repository
2. Go to repository Settings > Pages
3. Select source branch (usually `main`)
4. Your site will be available at `https://username.github.io/repository-name`

### Netlify

1. Connect your GitHub repository to Netlify
2. Set build command to empty (static site)
3. Set publish directory to `/` (root)
4. Deploy automatically on git push

### Manual Hosting

Upload all files to any web server. No special configuration needed.

## 📱 Browser Support

- Chrome/Edge 80+
- Firefox 75+
- Safari 13+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Development

No build process required! Just:

1. Edit files directly
2. Open `index.html` in a browser
3. Refresh to see changes

For local development with live reload, you can use:
- Live Server (VS Code extension)
- Python: `python -m http.server 8000`
- Node.js: `npx serve`

## 📝 Notes

- This is **not affiliated with Riot Games**
- Uses fictional team names and players
- Designed for community tournaments only
- All data is stored in JavaScript files for easy manual editing
- No database or backend required

## 🤝 Contributing

Feel free to:
- Add new features
- Improve the design
- Fix bugs
- Add more tournament formats

## 📄 License

Free to use for community tournaments. Not for commercial use.
