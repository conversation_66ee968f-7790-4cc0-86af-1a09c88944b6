<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teams - Community Valorant Tournaments</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/teams.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>VALORANT<span class="accent">COMMUNITY</span></h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">HOME</a>
                </li>
                <li class="nav-item">
                    <a href="teams.html" class="nav-link active">TEAMS</a>
                </li>
                <li class="nav-item">
                    <a href="schedule.html" class="nav-link">SCHEDULE</a>
                </li>
                <li class="nav-item">
                    <a href="bracket.html" class="nav-link">BRACKET</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="teams-page-header">
        <div class="container">
            <div class="header-content">
                <h1 class="page-title">TOURNAMENT TEAMS</h1>
                <p class="page-description">Meet the 8 teams competing in Community Clash Spring 2024</p>

                <!-- Quick Stats -->
                <div class="quick-stats">
                    <div class="quick-stat">
                        <span class="stat-number" id="total-teams-header">8</span>
                        <span class="stat-label">Total Teams</span>
                    </div>
                    <div class="quick-stat">
                        <span class="stat-number" id="active-teams-header">2</span>
                        <span class="stat-label">Still Active</span>
                    </div>
                    <div class="quick-stat">
                        <span class="stat-number" id="total-players-header">40</span>
                        <span class="stat-label">Total Players</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Controls -->
    <section class="teams-controls">
        <div class="container">
            <div class="controls-wrapper">
                <div class="filter-section">
                    <h3 class="control-title">Filter Teams</h3>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">ALL TEAMS</button>
                        <button class="filter-btn" data-filter="active">ACTIVE</button>
                        <button class="filter-btn" data-filter="eliminated">ELIMINATED</button>
                    </div>
                </div>

                <div class="sort-section">
                    <h3 class="control-title">Sort By</h3>
                    <select id="sort-select" class="sort-select">
                        <option value="name-asc">Name (A-Z)</option>
                        <option value="name-desc">Name (Z-A)</option>
                        <option value="record-desc">Best Record</option>
                        <option value="record-asc">Worst Record</option>
                        <option value="status-asc">Status</option>
                    </select>
                </div>

                <div class="view-section">
                    <h3 class="control-title">View</h3>
                    <div class="view-buttons">
                        <button class="view-btn active" data-view="grid">
                            <span>⊞</span> Grid
                        </button>
                        <button class="view-btn" data-view="list">
                            <span>☰</span> List
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Teams Display -->
    <section class="teams-display">
        <div class="container">
            <div class="teams-container">
                <div class="teams-grid" id="teams-grid">
                    <!-- Teams will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </section>

    <!-- Detailed Statistics -->
    <section class="detailed-stats">
        <div class="container">
            <h2 class="section-title">DETAILED STATISTICS</h2>
            <div class="stats-container">
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-icon">🏆</div>
                        <div class="stat-content">
                            <h3>TOURNAMENT CHAMPION</h3>
                            <span class="stat-value" id="champion-team">TBD</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⚔️</div>
                        <div class="stat-content">
                            <h3>MATCHES PLAYED</h3>
                            <span class="stat-value" id="total-matches">7</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <h3>MAPS PLAYED</h3>
                            <span class="stat-value" id="total-maps">15</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <h3>PRIZE POOL</h3>
                            <span class="stat-value">$500</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Community Valorant Tournaments. Not affiliated with Riot Games.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data/teams.js"></script>
    <script src="js/data/tournaments.js"></script>
    <script src="js/data/matches.js"></script>
    <script src="js/components/navigation.js"></script>
    <script src="js/components/team-card.js"></script>
    <script src="js/pages/teams.js"></script>
</body>
</html>
