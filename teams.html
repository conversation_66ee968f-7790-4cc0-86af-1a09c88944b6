<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teams - Community Valorant Tournaments</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>VALORANT<span class="accent">COMMUNITY</span></h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">HOME</a>
                </li>
                <li class="nav-item">
                    <a href="teams.html" class="nav-link active">TEAMS</a>
                </li>
                <li class="nav-item">
                    <a href="schedule.html" class="nav-link">SCHEDULE</a>
                </li>
                <li class="nav-item">
                    <a href="bracket.html" class="nav-link">BRACKET</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">TOURNAMENT TEAMS</h1>
            <p class="page-description">Meet the 8 teams competing in Community Clash Spring 2024</p>
        </div>
    </section>

    <!-- Team Filters -->
    <section class="team-filters">
        <div class="container">
            <div class="filter-controls">
                <h3>Filter Teams:</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">ALL TEAMS</button>
                    <button class="filter-btn" data-filter="active">ACTIVE</button>
                    <button class="filter-btn" data-filter="eliminated">ELIMINATED</button>
                </div>
            </div>
            
            <div class="sort-controls">
                <h3>Sort By:</h3>
                <select id="sort-select" class="sort-select">
                    <option value="name-asc">Name (A-Z)</option>
                    <option value="name-desc">Name (Z-A)</option>
                    <option value="record-desc">Best Record</option>
                    <option value="record-asc">Worst Record</option>
                    <option value="status-asc">Status</option>
                </select>
            </div>
        </div>
    </section>

    <!-- Teams Grid -->
    <section class="teams-section">
        <div class="container">
            <div class="teams-grid" id="teams-grid">
                <!-- Teams will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Tournament Stats -->
    <section class="tournament-stats">
        <div class="container">
            <h2 class="section-title">TOURNAMENT STATISTICS</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>TOTAL TEAMS</h3>
                    <span class="stat-value" id="total-teams">8</span>
                </div>
                <div class="stat-card">
                    <h3>ACTIVE TEAMS</h3>
                    <span class="stat-value" id="active-teams">2</span>
                </div>
                <div class="stat-card">
                    <h3>ELIMINATED TEAMS</h3>
                    <span class="stat-value" id="eliminated-teams">6</span>
                </div>
                <div class="stat-card">
                    <h3>TOTAL PLAYERS</h3>
                    <span class="stat-value" id="total-players">40</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Community Valorant Tournaments. Not affiliated with Riot Games.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data/teams.js"></script>
    <script src="js/data/tournaments.js"></script>
    <script src="js/data/matches.js"></script>
    <script src="js/components/navigation.js"></script>
    <script src="js/components/team-card.js"></script>
    <script src="js/pages/teams.js"></script>
</body>
</html>
