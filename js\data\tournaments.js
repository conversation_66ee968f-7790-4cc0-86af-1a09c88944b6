/**
 * TOURNAMENTS DATA
 * 
 * This file contains tournament information and configuration.
 * 
 * TO CREATE A NEW TOURNAMENT:
 * 1. Add a new tournament object to the tournaments array
 * 2. Update the currentTournament variable
 * 3. Make sure all team IDs in the bracket exist in teams.js
 * 4. Update match data in matches.js accordingly
 */

const tournaments = [
    {
        id: 'community-clash-spring-2024',
        name: 'Community Clash',
        subtitle: 'Spring 2024',
        description: '8 teams compete in single elimination for community glory',
        format: 'Single Elimination',
        prizePool: '$500',
        startDate: '2024-03-15',
        endDate: '2024-03-17',
        status: 'active', // active, completed, upcoming
        
        // Tournament schedule
        schedule: {
            'Quarterfinals': '2024-03-15',
            'Semifinals': '2024-03-16',
            'Grand Final': '2024-03-17'
        },
        
        // Tournament rules
        rules: [
            'Standard Valorant competitive rules',
            'Best of 3 matches (except Grand Final)',
            'Best of 5 Grand Final',
            'Map picks and bans',
            'No coaching during matches',
            'Standard timeout rules apply'
        ],
        
        // Prize distribution
        prizes: {
            '1st': '$300',
            '2nd': '$150',
            '3rd-4th': '$25 each'
        },
        
        // Tournament bracket structure
        bracket: {
            // Quarterfinals (Round 1)
            quarterfinals: [
                {
                    id: 'qf1',
                    team1: 'phoenix-rising',
                    team2: 'digital-demons',
                    winner: 'phoenix-rising',
                    status: 'completed'
                },
                {
                    id: 'qf2',
                    team1: 'cyber-wolves',
                    team2: 'crimson-tide',
                    winner: 'cyber-wolves',
                    status: 'completed'
                },
                {
                    id: 'qf3',
                    team1: 'neon-knights',
                    team2: 'void-hunters',
                    winner: 'neon-knights',
                    status: 'completed'
                },
                {
                    id: 'qf4',
                    team1: 'shadow-strike',
                    team2: 'storm-breakers',
                    winner: 'shadow-strike',
                    status: 'completed'
                }
            ],
            
            // Semifinals (Round 2)
            semifinals: [
                {
                    id: 'sf1',
                    team1: 'phoenix-rising', // Winner of qf1
                    team2: 'cyber-wolves',   // Winner of qf2
                    winner: 'phoenix-rising',
                    status: 'completed'
                },
                {
                    id: 'sf2',
                    team1: 'neon-knights',   // Winner of qf3
                    team2: 'shadow-strike',  // Winner of qf4
                    winner: null,
                    status: 'upcoming'
                }
            ],
            
            // Grand Final (Round 3)
            final: {
                id: 'final',
                team1: 'phoenix-rising', // Winner of sf1
                team2: null,             // Winner of sf2 (TBD)
                winner: null,
                status: 'upcoming'
            }
        },
        
        // Map pool for the tournament
        mapPool: [
            'Ascent',
            'Bind',
            'Haven',
            'Split',
            'Icebox',
            'Breeze',
            'Fracture'
        ]
    }
];

// Current active tournament
const currentTournament = tournaments[0];

/**
 * UTILITY FUNCTIONS FOR TOURNAMENTS
 */

// Get current tournament
function getCurrentTournament() {
    return currentTournament;
}

// Get tournament by ID
function getTournamentById(tournamentId) {
    return tournaments.find(tournament => tournament.id === tournamentId);
}

// Get tournament status
function getTournamentStatus() {
    return currentTournament.status;
}

// Get next match in the tournament
function getNextMatch() {
    const tournament = getCurrentTournament();
    
    // Check semifinals first
    for (let match of tournament.bracket.semifinals) {
        if (match.status === 'upcoming') {
            return match;
        }
    }
    
    // Check final
    if (tournament.bracket.final.status === 'upcoming') {
        return tournament.bracket.final;
    }
    
    return null;
}

// Get completed matches
function getCompletedMatches() {
    const tournament = getCurrentTournament();
    const completed = [];
    
    // Add completed quarterfinals
    tournament.bracket.quarterfinals.forEach(match => {
        if (match.status === 'completed') {
            completed.push(match);
        }
    });
    
    // Add completed semifinals
    tournament.bracket.semifinals.forEach(match => {
        if (match.status === 'completed') {
            completed.push(match);
        }
    });
    
    // Add completed final
    if (tournament.bracket.final.status === 'completed') {
        completed.push(tournament.bracket.final);
    }
    
    return completed;
}

// Get upcoming matches
function getUpcomingMatches() {
    const tournament = getCurrentTournament();
    const upcoming = [];
    
    // Add upcoming semifinals
    tournament.bracket.semifinals.forEach(match => {
        if (match.status === 'upcoming') {
            upcoming.push(match);
        }
    });
    
    // Add upcoming final
    if (tournament.bracket.final.status === 'upcoming') {
        upcoming.push(tournament.bracket.final);
    }
    
    return upcoming;
}

// Update match result
function updateMatchResult(matchId, winnerId) {
    const tournament = getCurrentTournament();
    
    // Find and update the match
    const allMatches = [
        ...tournament.bracket.quarterfinals,
        ...tournament.bracket.semifinals,
        tournament.bracket.final
    ];
    
    const match = allMatches.find(m => m.id === matchId);
    if (match) {
        match.winner = winnerId;
        match.status = 'completed';
        
        // Update team records
        updateTeamRecord(winnerId, true);
        const loserId = match.team1 === winnerId ? match.team2 : match.team1;
        updateTeamRecord(loserId, false);
        eliminateTeam(loserId);
    }
}

// Get tournament progress percentage
function getTournamentProgress() {
    const tournament = getCurrentTournament();
    const totalMatches = tournament.bracket.quarterfinals.length + 
                        tournament.bracket.semifinals.length + 1; // +1 for final
    
    const completedMatches = getCompletedMatches().length;
    
    return Math.round((completedMatches / totalMatches) * 100);
}

// Export for use in other files (if using modules)
// export { tournaments, currentTournament, getCurrentTournament, getTournamentById, getNextMatch, getCompletedMatches, getUpcomingMatches, updateMatchResult, getTournamentProgress };
