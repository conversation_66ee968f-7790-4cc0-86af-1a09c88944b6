/**
 * HOME PAGE FUNCTIONALITY
 * 
 * Handles the home page display including upcoming matches,
 * recent results, and tournament information.
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeHomePage();
});

function initializeHomePage() {
    loadUpcomingMatches();
    loadRecentResults();
    updateTournamentStats();
    
    // Set up auto-refresh for live matches (every 30 seconds)
    setInterval(refreshLiveContent, 30000);
}

/**
 * Load and display upcoming matches
 */
function loadUpcomingMatches() {
    const container = document.getElementById('upcoming-matches');
    if (!container) {
        console.error('Upcoming matches container not found');
        return;
    }

    // Get next 3 upcoming matches
    const upcomingMatches = getUpcomingMatchesLimit(3);
    
    if (upcomingMatches.length === 0) {
        container.innerHTML = `
            <div class="no-matches">
                <h3>No Upcoming Matches</h3>
                <p>All matches have been completed or the tournament has ended.</p>
            </div>
        `;
        return;
    }

    // Render match cards
    renderMatchCards(upcomingMatches, container, false);
    
    // Add click interactions
    addMatchCardInteractions(container);
}

/**
 * Load and display recent match results
 */
function loadRecentResults() {
    const container = document.getElementById('recent-results');
    if (!container) {
        console.error('Recent results container not found');
        return;
    }

    // Get last 3 completed matches
    const recentMatches = getRecentMatches(3);
    
    if (recentMatches.length === 0) {
        container.innerHTML = `
            <div class="no-matches">
                <h3>No Recent Results</h3>
                <p>No matches have been completed yet.</p>
            </div>
        `;
        return;
    }

    // Render match cards with details
    renderMatchCards(recentMatches, container, true);
    
    // Add click interactions
    addMatchCardInteractions(container);
}

/**
 * Update tournament statistics in the hero section
 */
function updateTournamentStats() {
    const tournament = getCurrentTournament();
    if (!tournament) return;

    // Update tournament progress
    const progress = getTournamentProgress();
    
    // Update hero stats if elements exist
    updateStatElement('.stat-number', 0, teams.length.toString());
    updateStatElement('.stat-number', 1, tournament.prizePool);
    
    // Calculate days remaining or elapsed
    const startDate = new Date(tournament.startDate);
    const endDate = new Date(tournament.endDate);
    const today = new Date();
    
    let daysText;
    if (today < startDate) {
        const daysUntilStart = Math.ceil((startDate - today) / (1000 * 60 * 60 * 24));
        daysText = `${daysUntilStart} days until start`;
    } else if (today <= endDate) {
        const daysRemaining = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
        daysText = daysRemaining > 0 ? `${daysRemaining} days left` : 'Final day!';
    } else {
        daysText = 'Tournament ended';
    }
    
    updateStatElement('.stat-number', 2, daysText.split(' ')[0]);
    updateStatElement('.stat-label', 2, daysText.split(' ').slice(1).join(' ').toUpperCase());
}

/**
 * Update a stat element by index
 * @param {string} selector - CSS selector for stat elements
 * @param {number} index - Index of the element to update
 * @param {string} value - New value to set
 */
function updateStatElement(selector, index, value) {
    const elements = document.querySelectorAll(selector);
    if (elements[index]) {
        elements[index].textContent = value;
    }
}

/**
 * Refresh live content (matches, scores, etc.)
 */
function refreshLiveContent() {
    const liveMatches = getLiveMatches();
    
    if (liveMatches.length > 0) {
        console.log('Refreshing live matches:', liveMatches.length);
        
        // Update upcoming matches section
        const upcomingContainer = document.getElementById('upcoming-matches');
        if (upcomingContainer) {
            updateLiveMatches(upcomingContainer);
        }
        
        // Update recent results section
        const resultsContainer = document.getElementById('recent-results');
        if (resultsContainer) {
            updateLiveMatches(resultsContainer);
        }
    }
}

/**
 * Handle tournament status updates
 */
function handleTournamentStatusUpdate() {
    const tournament = getCurrentTournament();
    
    switch (tournament.status) {
        case 'upcoming':
            showTournamentCountdown();
            break;
        case 'active':
            showActiveTournamentInfo();
            break;
        case 'completed':
            showTournamentResults();
            break;
    }
}

/**
 * Show countdown to tournament start
 */
function showTournamentCountdown() {
    const tournament = getCurrentTournament();
    const startDate = new Date(tournament.startDate);
    const now = new Date();
    
    if (startDate > now) {
        const timeUntilStart = startDate - now;
        const days = Math.floor(timeUntilStart / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeUntilStart % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        
        // Update hero section with countdown
        const heroDescription = document.querySelector('.hero-description');
        if (heroDescription) {
            heroDescription.textContent = `Tournament starts in ${days} days and ${hours} hours`;
        }
    }
}

/**
 * Show active tournament information
 */
function showActiveTournamentInfo() {
    const nextMatch = getNextMatch();
    
    if (nextMatch) {
        const heroDescription = document.querySelector('.hero-description');
        if (heroDescription) {
            const team1 = getTeamById(nextMatch.team1);
            const team2 = getTeamById(nextMatch.team2);
            
            if (team1 && team2) {
                heroDescription.textContent = `Next up: ${team1.name} vs ${team2.name}`;
            }
        }
    }
}

/**
 * Show tournament completion results
 */
function showTournamentResults() {
    const tournament = getCurrentTournament();
    const finalMatch = tournament.bracket.final;
    
    if (finalMatch.winner) {
        const winner = getTeamById(finalMatch.winner);
        const heroDescription = document.querySelector('.hero-description');
        
        if (heroDescription && winner) {
            heroDescription.textContent = `Tournament Champion: ${winner.name}!`;
        }
    }
}

/**
 * Add scroll animations for better UX
 */
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe sections for animation
    const sections = document.querySelectorAll('.upcoming-matches, .recent-results, .tournament-info');
    sections.forEach(section => {
        observer.observe(section);
    });
}

// Initialize scroll animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addScrollAnimations();
});

// Export functions for use in other scripts
window.HomePage = {
    initializeHomePage,
    loadUpcomingMatches,
    loadRecentResults,
    updateTournamentStats,
    refreshLiveContent
};
