/* ===== TEAMS PAGE STYLES ===== */

/* Reset existing styles */
.teams-page-header,
.teams-controls,
.teams-display,
.detailed-stats {
    all: unset;
    display: block;
}

/* Page Header */
.teams-page-header {
    background: #0F1419;
    padding: 100px 0 40px;
    margin-top: 70px;
    text-align: center;
}

.header-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.page-title {
    font-size: 2.5rem;
    color: #FFFFFF;
    margin-bottom: 10px;
    font-weight: 600;
}

.page-description {
    font-size: 1.1rem;
    color: #AAABAD;
    margin-bottom: 30px;
}

/* Quick Stats */
.quick-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
}

.quick-stat {
    text-align: center;
    padding: 20px;
    background: #1E2328;
    border-radius: 8px;
    min-width: 120px;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    color: #FF4655;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #AAABAD;
    text-transform: uppercase;
}

/* Controls Section */
.teams-controls {
    background: #1E2328;
    padding: 20px 0;
    border-bottom: 1px solid #3C3C41;
}

.controls-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-section,
.sort-section,
.view-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-title {
    color: #FF4655;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.filter-buttons,
.view-buttons {
    display: flex;
    gap: 8px;
}

.filter-btn,
.view-btn {
    background: transparent;
    border: 1px solid #3C3C41;
    color: #AAABAD;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.filter-btn:hover,
.view-btn:hover,
.filter-btn.active,
.view-btn.active {
    background: #FF4655;
    border-color: #FF4655;
    color: white;
}

.sort-select {
    background: #0F1419;
    border: 1px solid #3C3C41;
    color: #FFFFFF;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
}

/* Teams Display */
.teams-display {
    background: #0F1419;
    padding: 40px 0;
    min-height: 500px;
}

.teams-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Team Cards */
.team-card {
    background: #1E2328;
    border: 1px solid #3C3C41;
    border-radius: 8px;
    padding: 20px;
    transition: border-color 0.2s ease;
}

.team-card:hover {
    border-color: #FF4655;
}

.team-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #3C3C41;
}

.team-card-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #FF4655;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
    color: white;
}

.team-card-info {
    flex: 1;
}

.team-card-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #FFFFFF;
    margin-bottom: 5px;
}

.team-card-record {
    color: #AAABAD;
    font-size: 0.85rem;
}

.team-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 5px;
    display: inline-block;
}

.team-status.active {
    background: rgba(0, 245, 160, 0.2);
    color: #00F5A0;
}

.team-status.eliminated {
    background: rgba(170, 171, 173, 0.2);
    color: #AAABAD;
}

/* Players Section */
.team-players h4 {
    color: #FF4655;
    font-size: 0.85rem;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.players-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.player {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.player-name {
    color: #FFFFFF;
    font-size: 0.9rem;
}

.player-role {
    color: #AAABAD;
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

/* Statistics Section */
.detailed-stats {
    background: #1E2328;
    padding: 40px 0;
}

.stats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: #0F1419;
    border: 1px solid #3C3C41;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 70, 85, 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-content h3 {
    color: #AAABAD;
    font-size: 0.85rem;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.stat-content .stat-value {
    font-size: 1.3rem;
    font-weight: 600;
    color: #FF4655;
}

/* Section Titles */
.section-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
    color: #FFFFFF;
    font-weight: 600;
    text-transform: uppercase;
}

.section-title::after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #FF4655;
    margin: 10px auto 0;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .quick-stats {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .quick-stat {
        min-width: 200px;
    }

    .controls-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .filter-section,
    .sort-section,
    .view-section {
        align-items: center;
        text-align: center;
    }

    .teams-grid {
        grid-template-columns: 1fr;
    }

    .stats-row {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 480px) {
    .page-title {
        font-size: 1.8rem;
    }

    .quick-stat {
        min-width: 150px;
        padding: 15px;
    }

    .team-card {
        padding: 15px;
    }

    .team-card-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .team-card-logo {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }
}
