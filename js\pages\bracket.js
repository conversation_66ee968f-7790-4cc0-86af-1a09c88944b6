/**
 * BRACKET PAGE FUNCTIONALITY
 *
 * Handles the tournament bracket display including match visualization,
 * progression tracking, and championship information.
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeBracketPage();
});

function initializeBracketPage() {
    loadTournamentBracket();
    updateBracketProgress();
    loadChampionshipInfo();
    updateBracketStats();
    updateTournamentSummary();
}

/**
 * Load and display the tournament bracket
 */
function loadTournamentBracket() {
    const container = document.getElementById('tournament-bracket');
    if (!container) {
        console.error('Tournament bracket container not found');
        return;
    }

    const tournament = getCurrentTournament();
    const bracket = createBracketVisualization(tournament);
    container.appendChild(bracket);
}

/**
 * Create the bracket visualization
 * @param {Object} tournament - Tournament data
 * @returns {HTMLElement} - Bracket element
 */
function createBracketVisualization(tournament) {
    const bracket = document.createElement('div');
    bracket.className = 'bracket-rounds';

    // Create quarterfinals round
    const quarterfinalsRound = createBracketRound('Quarterfinals', tournament.bracket.quarterfinals);
    bracket.appendChild(quarterfinalsRound);

    // Create semifinals round
    const semifinalsRound = createBracketRound('Semifinals', tournament.bracket.semifinals);
    bracket.appendChild(semifinalsRound);

    // Create final round
    const finalRound = createBracketRound('Grand Final', [tournament.bracket.final]);
    bracket.appendChild(finalRound);

    return bracket;
}

/**
 * Create a bracket round
 * @param {string} roundName - Name of the round
 * @param {Array} matches - Array of matches in this round
 * @returns {HTMLElement} - Round element
 */
function createBracketRound(roundName, matches) {
    const round = document.createElement('div');
    round.className = 'bracket-round';

    const roundTitle = document.createElement('div');
    roundTitle.className = 'bracket-round-title';
    roundTitle.textContent = roundName;
    round.appendChild(roundTitle);

    matches.forEach(match => {
        const matchElement = createBracketMatch(match);
        round.appendChild(matchElement);
    });

    return round;
}

/**
 * Create a bracket match element
 * @param {Object} match - Match data
 * @returns {HTMLElement} - Match element
 */
function createBracketMatch(match) {
    const matchElement = document.createElement('div');
    matchElement.className = `bracket-match ${match.status}`;
    matchElement.setAttribute('data-match-id', match.id);

    const team1 = getTeamById(match.team1);
    const team2 = getTeamById(match.team2);

    if (!team1 || !team2) {
        return createPlaceholderBracketMatch();
    }

    const matchData = getMatchByBracketId(match.id);
    const score1 = matchData && matchData.score ? matchData.score.team1 : 0;
    const score2 = matchData && matchData.score ? matchData.score.team2 : 0;

    matchElement.innerHTML = `
        <div class="bracket-team ${match.winner === match.team1 ? 'winner' : ''}">
            <span class="bracket-team-name">${team1.name}</span>
            <span class="bracket-team-score">${match.status === 'completed' ? score1 : ''}</span>
        </div>
        <div class="bracket-team ${match.winner === match.team2 ? 'winner' : ''}">
            <span class="bracket-team-name">${team2 ? team2.name : 'TBD'}</span>
            <span class="bracket-team-score">${match.status === 'completed' ? score2 : ''}</span>
        </div>
        <div class="bracket-match-info">
            <span class="match-status ${match.status}">${match.status.toUpperCase()}</span>
        </div>
    `;

    // Add click handler for match details
    matchElement.addEventListener('click', () => showBracketMatchDetails(match.id));

    return matchElement;
}

/**
 * Create placeholder bracket match
 * @returns {HTMLElement} - Placeholder match element
 */
function createPlaceholderBracketMatch() {
    const matchElement = document.createElement('div');
    matchElement.className = 'bracket-match placeholder';

    matchElement.innerHTML = `
        <div class="bracket-team">
            <span class="bracket-team-name">TBD</span>
            <span class="bracket-team-score"></span>
        </div>
        <div class="bracket-team">
            <span class="bracket-team-name">TBD</span>
            <span class="bracket-team-score"></span>
        </div>
        <div class="bracket-match-info">
            <span class="match-status upcoming">TBD</span>
        </div>
    `;

    return matchElement;
}

/**
 * Update bracket progress and stats
 */
function updateBracketProgress() {
    const progressElement = document.getElementById('bracket-progress');
    if (progressElement) {
        const progress = getTournamentProgress();
        progressElement.textContent = `${progress}%`;
    }
}

/**
 * Update bracket statistics in header
 */
function updateBracketStats() {
    const matchesCompletedEl = document.getElementById('matches-completed');

    if (matchesCompletedEl) {
        const completedMatches = getMatchesByStatus('completed').length;
        const totalMatches = matches.length;
        matchesCompletedEl.textContent = `${completedMatches}/${totalMatches}`;
    }
}

/**
 * Load championship information
 */
function loadChampionshipInfo() {
    const container = document.getElementById('championship-card');
    if (!container) {
        console.error('Championship card container not found');
        return;
    }

    const tournament = getCurrentTournament();
    const championshipCard = createChampionshipCard(tournament);
    container.appendChild(championshipCard);
}

/**
 * Create championship information card
 * @param {Object} tournament - Tournament data
 * @returns {HTMLElement} - Championship card element
 */
function createChampionshipCard(tournament) {
    const card = document.createElement('div');
    card.className = 'championship-details';

    const finalMatch = tournament.bracket.final;
    let championshipContent;

    if (finalMatch.winner) {
        // Tournament completed
        const champion = getTeamById(finalMatch.winner);
        const runnerUp = getTeamById(finalMatch.team1 === finalMatch.winner ? finalMatch.team2 : finalMatch.team1);

        championshipContent = `
            <h2>🏆 TOURNAMENT CHAMPION</h2>
            <div class="champion-info">
                <div class="champion-team">
                    <div class="team-logo large">${champion.logo}</div>
                    <h3>${champion.name}</h3>
                    <p>Congratulations to the champions!</p>
                </div>
                <div class="runner-up">
                    <h4>Runner-up: ${runnerUp.name}</h4>
                </div>
            </div>
            <div class="prize-info">
                <h4>Prize Distribution:</h4>
                <ul>
                    <li>1st Place: ${tournament.prizes['1st']}</li>
                    <li>2nd Place: ${tournament.prizes['2nd']}</li>
                    <li>3rd-4th Place: ${tournament.prizes['3rd-4th']}</li>
                </ul>
            </div>
        `;
    } else if (finalMatch.team1 && finalMatch.team2) {
        // Final match ready
        const team1 = getTeamById(finalMatch.team1);
        const team2 = getTeamById(finalMatch.team2);

        championshipContent = `
            <h2>🏆 GRAND FINAL</h2>
            <div class="final-matchup">
                <div class="finalist">
                    <div class="team-logo">${team1.logo}</div>
                    <h3>${team1.name}</h3>
                </div>
                <div class="vs-final">VS</div>
                <div class="finalist">
                    <div class="team-logo">${team2.logo}</div>
                    <h3>${team2.name}</h3>
                </div>
            </div>
            <div class="final-info">
                <p>Best of 5 match for the championship!</p>
                <p>Prize Pool: ${tournament.prizePool}</p>
            </div>
        `;
    } else {
        // Awaiting finalists
        championshipContent = `
            <h2>🏆 CHAMPIONSHIP</h2>
            <div class="awaiting-finalists">
                <p>Awaiting finalists...</p>
                <p>The Grand Final will be a Best of 5 match</p>
                <p>Total Prize Pool: ${tournament.prizePool}</p>
            </div>
        `;
    }

    card.innerHTML = championshipContent;
    return card;
}

/**
 * Show detailed match information for bracket match
 * @param {string} bracketMatchId - Bracket match ID
 */
function showBracketMatchDetails(bracketMatchId) {
    const match = getMatchByBracketId(bracketMatchId);
    if (!match) {
        console.error('Match not found:', bracketMatchId);
        return;
    }

    // Create and show match details modal
    const modal = createMatchDetailsModal(match);
    document.body.appendChild(modal);
}

/**
 * Create match details modal
 * @param {Object} match - Match data
 * @returns {HTMLElement} - Modal element
 */
function createMatchDetailsModal(match) {
    const modal = document.createElement('div');
    modal.className = 'match-details-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    `;

    const modalContent = document.createElement('div');
    modalContent.className = 'match-details-content';
    modalContent.style.cssText = `
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 2rem;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    `;

    const team1 = getTeamById(match.team1);
    const team2 = getTeamById(match.team2);

    let mapsHTML = '';
    if (match.maps && match.maps.length > 0) {
        mapsHTML = `
            <div class="maps-results">
                <h4>Map Results:</h4>
                ${match.maps.map(map => `
                    <div class="map-result">
                        <span class="map-name">${map.name}</span>
                        <span class="map-score">${map.score.team1}-${map.score.team2}</span>
                        <span class="map-winner">${getTeamById(map.winner)?.name || 'TBD'}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    modalContent.innerHTML = `
        <div class="modal-header">
            <h2>${match.round}</h2>
            <button class="close-btn" onclick="closeMatchModal(this.closest('.match-details-modal'))">&times;</button>
        </div>

        <div class="match-details">
            <div class="teams-matchup">
                <div class="team ${match.winner === match.team1 ? 'winner' : ''}">
                    <div class="team-logo">${team1.logo}</div>
                    <h3>${team1.name}</h3>
                </div>
                <div class="match-score">
                    <span class="score">${getMatchScoreString(match)}</span>
                    <span class="status ${match.status}">${match.status.toUpperCase()}</span>
                </div>
                <div class="team ${match.winner === match.team2 ? 'winner' : ''}">
                    <div class="team-logo">${team2 ? team2.logo : '?'}</div>
                    <h3>${team2 ? team2.name : 'TBD'}</h3>
                </div>
            </div>

            ${mapsHTML}

            <div class="match-info">
                <p><strong>Scheduled:</strong> ${formatMatchTime(match.scheduledTime)}</p>
                ${match.actualStartTime ? `<p><strong>Started:</strong> ${formatMatchTime(match.actualStartTime)}</p>` : ''}
                ${match.endTime ? `<p><strong>Ended:</strong> ${formatMatchTime(match.endTime)}</p>` : ''}
                ${getMatchDuration(match) ? `<p><strong>Duration:</strong> ${getMatchDuration(match)} minutes</p>` : ''}
            </div>
        </div>
    `;

    modal.appendChild(modalContent);

    // Close on click outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeMatchModal(modal);
        }
    });

    return modal;
}

/**
 * Close match details modal
 * @param {HTMLElement} modal - Modal element to close
 */
function closeMatchModal(modal) {
    if (modal && modal.parentNode) {
        modal.parentNode.removeChild(modal);
    }
}

/**
 * Reset bracket view to default
 */
function resetBracketView() {
    const container = document.getElementById('tournament-bracket');
    if (container) {
        container.scrollLeft = 0;
    }
}

/**
 * Export bracket as image or PDF
 */
function exportBracket() {
    // For now, just export the data
    const tournament = getCurrentTournament();
    const bracketData = {
        tournament: tournament.name + ' ' + tournament.subtitle,
        bracket: tournament.bracket,
        exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(bracketData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'tournament-bracket.json';
    link.click();
}

/**
 * Update tournament summary section
 */
function updateTournamentSummary() {
    const championName = document.getElementById('champion-name');
    const runnerUpName = document.getElementById('runner-up-name');
    const totalMapsPlayed = document.getElementById('total-maps-played');

    const tournament = getCurrentTournament();
    const finalMatch = tournament.bracket.final;

    // Update champion
    if (championName && finalMatch.winner) {
        const champion = getTeamById(finalMatch.winner);
        championName.textContent = champion ? champion.name : 'TBD';
    }

    // Update runner-up
    if (runnerUpName && finalMatch.winner && finalMatch.team1 && finalMatch.team2) {
        const runnerUpId = finalMatch.team1 === finalMatch.winner ? finalMatch.team2 : finalMatch.team1;
        const runnerUp = getTeamById(runnerUpId);
        runnerUpName.textContent = runnerUp ? runnerUp.name : 'TBD';
    }

    // Update total maps played
    if (totalMapsPlayed) {
        const totalMaps = matches.reduce((total, match) => {
            return total + (match.maps ? match.maps.length : 0);
        }, 0);
        totalMapsPlayed.textContent = `${totalMaps} Maps`;
    }
}

/**
 * Print bracket
 */
function printBracket() {
    window.print();
}

// Make functions available globally
window.showBracketMatchDetails = showBracketMatchDetails;
window.closeMatchModal = closeMatchModal;
window.resetBracketView = resetBracketView;
window.exportBracket = exportBracket;
window.printBracket = printBracket;
window.clearFilters = clearFilters;

// Export functions for use in other scripts
window.BracketPage = {
    initializeBracketPage,
    loadTournamentBracket,
    showBracketMatchDetails,
    resetBracketView,
    exportBracket,
    printBracket,
    updateTournamentSummary
};
