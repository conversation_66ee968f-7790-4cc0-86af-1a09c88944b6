/**
 * TEAMS PAGE FUNCTIONALITY
 * 
 * Handles the teams page display including team filtering,
 * sorting, and statistics.
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeTeamsPage();
});

function initializeTeamsPage() {
    loadAllTeams();
    setupTeamFilters();
    setupTeamSorting();
    updateTournamentStatistics();
}

/**
 * Load and display all teams
 */
function loadAllTeams() {
    const container = document.getElementById('teams-grid');
    if (!container) {
        console.error('Teams grid container not found');
        return;
    }

    // Render all teams with player information
    renderTeamCards(teams, container, true, false);
}

/**
 * Setup team filtering functionality
 */
function setupTeamFilters() {
    const filterContainer = document.querySelector('.filter-buttons');
    const teamsContainer = document.getElementById('teams-grid');
    
    if (!filterContainer || !teamsContainer) {
        console.error('Filter elements not found');
        return;
    }

    // Add filter functionality
    addTeamFilters(filterContainer, teamsContainer);
}

/**
 * Setup team sorting functionality
 */
function setupTeamSorting() {
    const sortSelect = document.getElementById('sort-select');
    const teamsContainer = document.getElementById('teams-grid');
    
    if (!sortSelect || !teamsContainer) {
        console.error('Sort elements not found');
        return;
    }

    sortSelect.addEventListener('change', function() {
        const [sortBy, order] = this.value.split('-');
        const currentFilter = getCurrentFilter();
        
        // Get teams based on current filter
        let teamsToSort;
        switch (currentFilter) {
            case 'active':
                teamsToSort = getActiveTeams();
                break;
            case 'eliminated':
                teamsToSort = getEliminatedTeams();
                break;
            default:
                teamsToSort = teams;
        }
        
        // Sort teams
        const sortedTeams = sortTeams(teamsToSort, sortBy, order);
        
        // Render sorted teams
        renderTeamCards(sortedTeams, teamsContainer, true, false);
    });
}

/**
 * Get currently active filter
 * @returns {string} - Current filter value
 */
function getCurrentFilter() {
    const activeFilterBtn = document.querySelector('.filter-btn.active');
    return activeFilterBtn ? activeFilterBtn.getAttribute('data-filter') : 'all';
}

/**
 * Update tournament statistics
 */
function updateTournamentStatistics() {
    const totalTeamsEl = document.getElementById('total-teams');
    const activeTeamsEl = document.getElementById('active-teams');
    const eliminatedTeamsEl = document.getElementById('eliminated-teams');
    const totalPlayersEl = document.getElementById('total-players');

    if (totalTeamsEl) {
        totalTeamsEl.textContent = teams.length;
    }

    if (activeTeamsEl) {
        activeTeamsEl.textContent = getActiveTeams().length;
    }

    if (eliminatedTeamsEl) {
        eliminatedTeamsEl.textContent = getEliminatedTeams().length;
    }

    if (totalPlayersEl) {
        const totalPlayers = teams.reduce((total, team) => {
            return total + (team.players ? team.players.length : 0);
        }, 0);
        totalPlayersEl.textContent = totalPlayers;
    }
}

/**
 * Search teams by name or player name
 * @param {string} searchTerm - Search term
 * @returns {Array} - Filtered teams
 */
function searchTeams(searchTerm) {
    if (!searchTerm || searchTerm.trim() === '') {
        return teams;
    }

    const term = searchTerm.toLowerCase().trim();
    
    return teams.filter(team => {
        // Search in team name
        if (team.name.toLowerCase().includes(term)) {
            return true;
        }
        
        // Search in player names
        if (team.players) {
            return team.players.some(player => 
                player.name.toLowerCase().includes(term) ||
                player.role.toLowerCase().includes(term)
            );
        }
        
        return false;
    });
}

/**
 * Add search functionality (if search input exists)
 */
function setupTeamSearch() {
    const searchInput = document.getElementById('team-search');
    const teamsContainer = document.getElementById('teams-grid');
    
    if (!searchInput || !teamsContainer) return;

    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        
        // Debounce search to avoid too many updates
        searchTimeout = setTimeout(() => {
            const searchTerm = this.value;
            const filteredTeams = searchTeams(searchTerm);
            
            // Apply current sorting
            const sortSelect = document.getElementById('sort-select');
            if (sortSelect) {
                const [sortBy, order] = sortSelect.value.split('-');
                const sortedTeams = sortTeams(filteredTeams, sortBy, order);
                renderTeamCards(sortedTeams, teamsContainer, true, false);
            } else {
                renderTeamCards(filteredTeams, teamsContainer, true, false);
            }
        }, 300);
    });
}

/**
 * Show team details in a modal or expanded view
 * @param {string} teamId - Team ID to show details for
 */
function showTeamDetails(teamId) {
    const team = getTeamById(teamId);
    if (!team) {
        console.error('Team not found:', teamId);
        return;
    }

    // Create a simple modal-like display
    const modal = createTeamModal(team);
    document.body.appendChild(modal);
    
    // Add click outside to close
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeTeamModal(modal);
        }
    });
}

/**
 * Create team details modal
 * @param {Object} team - Team data
 * @returns {HTMLElement} - Modal element
 */
function createTeamModal(team) {
    const modal = document.createElement('div');
    modal.className = 'team-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    `;

    const modalContent = document.createElement('div');
    modalContent.className = 'team-modal-content';
    modalContent.style.cssText = `
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    `;

    const recordString = `${team.record.wins}-${team.record.losses}`;
    const winRate = team.record.wins + team.record.losses > 0 ? 
        ((team.record.wins / (team.record.wins + team.record.losses)) * 100).toFixed(1) : 0;

    modalContent.innerHTML = `
        <div class="modal-header">
            <h2>${team.name}</h2>
            <button class="close-btn" onclick="closeTeamModal(this.closest('.team-modal'))">&times;</button>
        </div>
        
        <div class="team-details">
            <div class="team-logo-large">${team.logo}</div>
            
            <div class="team-stats">
                <div class="stat">
                    <label>Record:</label>
                    <span>${recordString}</span>
                </div>
                <div class="stat">
                    <label>Win Rate:</label>
                    <span>${winRate}%</span>
                </div>
                <div class="stat">
                    <label>Status:</label>
                    <span class="${team.status}">${team.status.toUpperCase()}</span>
                </div>
            </div>
            
            <div class="team-roster">
                <h3>Roster:</h3>
                ${team.players.map(player => `
                    <div class="player-detail">
                        <span class="player-name">${player.name}</span>
                        <span class="player-role" style="color: ${roleColors[player.role]}">${player.role}</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    modal.appendChild(modalContent);
    return modal;
}

/**
 * Close team details modal
 * @param {HTMLElement} modal - Modal element to close
 */
function closeTeamModal(modal) {
    if (modal && modal.parentNode) {
        modal.parentNode.removeChild(modal);
    }
}

/**
 * Export team data (for manual editing/backup)
 */
function exportTeamData() {
    const dataStr = JSON.stringify(teams, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'teams-data.json';
    link.click();
}

// Make functions available globally
window.showTeamDetails = showTeamDetails;
window.closeTeamModal = closeTeamModal;

// Export functions for use in other scripts
window.TeamsPage = {
    initializeTeamsPage,
    loadAllTeams,
    setupTeamFilters,
    setupTeamSorting,
    searchTeams,
    showTeamDetails,
    exportTeamData
};
