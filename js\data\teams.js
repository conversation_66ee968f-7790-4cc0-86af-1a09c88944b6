/**
 * TEAMS DATA
 * 
 * This file contains all team information for the tournament.
 * Each team has:
 * - id: unique identifier
 * - name: team name
 * - logo: team logo (using initials for now)
 * - players: array of 5 players with their roles
 * - record: wins-losses record
 * - status: active, eliminated, etc.
 * 
 * TO ADD A NEW TEAM:
 * 1. Add a new team object to the teams array
 * 2. Make sure the ID is unique
 * 3. Add 5 players with their roles
 * 4. Update the record as matches are played
 */

const teams = [
    {
        id: 'phoenix-rising',
        name: 'Phoenix Rising',
        logo: 'PR', // Using initials as logo placeholder
        players: [
            { name: 'FireStorm', role: 'Duelist' },
            { name: '<PERSON><PERSON>creen', role: 'Controller' },
            { name: '<PERSON><PERSON><PERSON><PERSON>', role: 'Initiator' },
            { name: '<PERSON>', role: '<PERSON>' },
            { name: '<PERSON>lexMaster', role: '<PERSON>lex' }
        ],
        record: { wins: 2, losses: 0 },
        status: 'active'
    },
    {
        id: 'cyber-wolves',
        name: 'Cyber Wolves',
        logo: '<PERSON>',
        players: [
            { name: '<PERSON><PERSON><PERSON><PERSON>', role: 'Duelist' },
            { name: '<PERSON><PERSON><PERSON><PERSON>', role: 'Controller' },
            { name: 'In<PERSON><PERSON><PERSON>', role: 'Initiator' },
            { name: '<PERSON><PERSON><PERSON><PERSON>', role: '<PERSON>' },
            { name: 'Adaptable', role: 'Flex' }
        ],
        record: { wins: 2, losses: 0 },
        status: 'active'
    },
    {
        id: 'neon-knights',
        name: 'Neon Knights',
        logo: 'NK',
        players: [
            { name: 'LightSpeed', role: 'Duelist' },
            { name: 'CloudMaster', role: 'Controller' },
            { name: 'Revealer', role: 'Initiator' },
            { name: 'Fortress', role: 'Sentinel' },
            { name: 'Versatile', role: 'Flex' }
        ],
        record: { wins: 1, losses: 1 },
        status: 'eliminated'
    },
    {
        id: 'shadow-strike',
        name: 'Shadow Strike',
        logo: 'SS',
        players: [
            { name: 'Phantom', role: 'Duelist' },
            { name: 'VeilMaster', role: 'Controller' },
            { name: 'Scout', role: 'Initiator' },
            { name: 'Anchor', role: 'Sentinel' },
            { name: 'AllRound', role: 'Flex' }
        ],
        record: { wins: 1, losses: 1 },
        status: 'eliminated'
    },
    {
        id: 'storm-breakers',
        name: 'Storm Breakers',
        logo: 'SB',
        players: [
            { name: 'Thunder', role: 'Duelist' },
            { name: 'Haze', role: 'Controller' },
            { name: 'Tracker', role: 'Initiator' },
            { name: 'Defender', role: 'Sentinel' },
            { name: 'Support', role: 'Flex' }
        ],
        record: { wins: 1, losses: 1 },
        status: 'eliminated'
    },
    {
        id: 'void-hunters',
        name: 'Void Hunters',
        logo: 'VH',
        players: [
            { name: 'Reaper', role: 'Duelist' },
            { name: 'Obscure', role: 'Controller' },
            { name: 'Seeker', role: 'Initiator' },
            { name: 'Bastion', role: 'Sentinel' },
            { name: 'Utility', role: 'Flex' }
        ],
        record: { wins: 1, losses: 1 },
        status: 'eliminated'
    },
    {
        id: 'crimson-tide',
        name: 'Crimson Tide',
        logo: 'CT',
        players: [
            { name: 'Blaze', role: 'Duelist' },
            { name: 'Smoker', role: 'Controller' },
            { name: 'Intel', role: 'Initiator' },
            { name: 'Watcher', role: 'Sentinel' },
            { name: 'Backup', role: 'Flex' }
        ],
        record: { wins: 1, losses: 1 },
        status: 'eliminated'
    },
    {
        id: 'digital-demons',
        name: 'Digital Demons',
        logo: 'DD',
        players: [
            { name: 'Inferno', role: 'Duelist' },
            { name: 'Vapor', role: 'Controller' },
            { name: 'Probe', role: 'Initiator' },
            { name: 'Shield', role: 'Sentinel' },
            { name: 'Switch', role: 'Flex' }
        ],
        record: { wins: 1, losses: 1 },
        status: 'eliminated'
    }
];

/**
 * UTILITY FUNCTIONS FOR TEAMS
 */

// Get team by ID
function getTeamById(teamId) {
    return teams.find(team => team.id === teamId);
}

// Get all active teams
function getActiveTeams() {
    return teams.filter(team => team.status === 'active');
}

// Get all eliminated teams
function getEliminatedTeams() {
    return teams.filter(team => team.status === 'eliminated');
}

// Get team record as string (e.g., "2-0")
function getTeamRecord(teamId) {
    const team = getTeamById(teamId);
    if (!team) return '0-0';
    return `${team.record.wins}-${team.record.losses}`;
}

// Update team record (call this when a match is completed)
function updateTeamRecord(teamId, won) {
    const team = getTeamById(teamId);
    if (!team) return;
    
    if (won) {
        team.record.wins++;
    } else {
        team.record.losses++;
    }
}

// Eliminate team (call this when a team loses in elimination tournament)
function eliminateTeam(teamId) {
    const team = getTeamById(teamId);
    if (team) {
        team.status = 'eliminated';
    }
}

/**
 * ROLE COLORS (for UI styling)
 */
const roleColors = {
    'Duelist': '#FF4655',
    'Controller': '#00D4FF',
    'Initiator': '#FFCC02',
    'Sentinel': '#00F5A0',
    'Flex': '#AAABAD'
};

// Export for use in other files (if using modules)
// export { teams, getTeamById, getActiveTeams, getEliminatedTeams, getTeamRecord, updateTeamRecord, eliminateTeam, roleColors };
