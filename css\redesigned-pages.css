/* ===== REDESIGNED PAGES STYLES ===== */

/* ===== TEAMS PAGE REDESIGN ===== */
.teams-page-header {
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    padding: 120px 0 80px;
    text-align: center;
    margin-top: 70px;
    position: relative;
    overflow: hidden;
}

.teams-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF4655" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 2;
}

.quick-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 3rem;
    flex-wrap: wrap;
}

.quick-stat {
    text-align: center;
}

.quick-stat .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-red);
    margin-bottom: 0.5rem;
}

.quick-stat .stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
}

.teams-controls {
    background: var(--card-bg);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.controls-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.filter-section,
.sort-section,
.view-section {
    text-align: center;
}

.control-title {
    color: var(--primary-red);
    font-size: 1rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.view-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.view-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-btn:hover,
.view-btn.active {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: white;
}

.teams-display {
    background: var(--dark-bg);
    padding: 4rem 0;
    min-height: 60vh;
}

.teams-container {
    position: relative;
}

.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.teams-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.teams-list .team-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    gap: 2rem;
}

.teams-list .team-card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 0 0 auto;
}

.teams-list .team-players {
    flex: 1;
    margin: 0;
}

.teams-list .players-list {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.teams-list .player {
    border: none;
    padding: 0.25rem 0.5rem;
    background: rgba(255, 70, 85, 0.1);
    border-radius: 4px;
    font-size: 0.8rem;
}

.detailed-stats {
    background: var(--card-bg);
    padding: 4rem 0;
}

.stats-container {
    margin-top: 2rem;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 70, 85, 0.1);
    border-radius: 50%;
}

.stat-content h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-content .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-red);
}

/* ===== SCHEDULE PAGE REDESIGN ===== */
.schedule-page-header {
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    padding: 120px 0 80px;
    text-align: center;
    margin-top: 70px;
    position: relative;
}

.tournament-progress {
    max-width: 500px;
    margin: 3rem auto 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.progress-percentage {
    color: var(--primary-red);
    font-weight: 700;
    font-size: 1.2rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-red), #FF6B7A);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.schedule-nav {
    background: var(--card-bg);
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.nav-tab {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 2rem;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tab:hover,
.nav-tab.active {
    color: var(--primary-red);
    border-bottom-color: var(--primary-red);
}

.schedule-filters {
    background: var(--dark-bg);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.filters-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-actions {
    display: flex;
    gap: 1rem;
}

.btn-secondary,
.btn-primary {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 0.9rem;
}

.btn-secondary {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-secondary:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.btn-primary {
    background: var(--primary-red);
    color: white;
}

.btn-primary:hover {
    background: #E63946;
}

.tab-content-container {
    min-height: 60vh;
}

.tab-content {
    display: none;
    padding: 4rem 0;
}

.tab-content.active {
    display: block;
}

.schedule-table-wrapper {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--card-bg);
}

.schedule-table th {
    background: var(--dark-bg);
    color: var(--primary-red);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
}

.schedule-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.schedule-table tr:hover {
    background: rgba(255, 70, 85, 0.05);
}

.timeline-wrapper {
    max-width: 800px;
    margin: 2rem auto;
}

/* ===== BRACKET PAGE REDESIGN ===== */
.bracket-page-header {
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    padding: 120px 0 80px;
    text-align: center;
    margin-top: 70px;
    position: relative;
}

.bracket-status {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 3rem;
    flex-wrap: wrap;
}

.status-item {
    text-align: center;
}

.status-label {
    display: block;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.status-value {
    display: block;
    color: var(--primary-red);
    font-size: 1.5rem;
    font-weight: 700;
}

.bracket-nav {
    background: var(--card-bg);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.nav-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.bracket-legend h3 {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.bracket-actions {
    display: flex;
    gap: 1rem;
}

.bracket-actions button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bracket-section {
    background: var(--dark-bg);
    padding: 4rem 0;
    min-height: 70vh;
}

.bracket-wrapper {
    position: relative;
}

.bracket-container {
    overflow-x: auto;
    padding: 2rem 0;
}

.bracket-scroll {
    min-width: 1000px;
}

.championship-spotlight {
    background: var(--card-bg);
    padding: 4rem 0;
}

.championship-wrapper {
    max-width: 800px;
    margin: 0 auto;
}

.tournament-summary {
    background: var(--dark-bg);
    padding: 4rem 0;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.summary-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
}

.summary-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 70, 85, 0.1);
    border-radius: 50%;
}

.summary-content h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.summary-content p {
    color: var(--primary-red);
    font-size: 1.2rem;
    font-weight: 600;
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 768px) {
    .quick-stats,
    .bracket-status {
        gap: 2rem;
    }

    .controls-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .teams-grid {
        grid-template-columns: 1fr;
    }

    .stats-row,
    .summary-grid {
        grid-template-columns: 1fr;
    }

    .nav-tabs {
        flex-direction: column;
        gap: 0;
    }

    .nav-tab {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
    }

    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-actions {
        justify-content: center;
    }

    .nav-wrapper {
        flex-direction: column;
        text-align: center;
    }

    .bracket-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}
