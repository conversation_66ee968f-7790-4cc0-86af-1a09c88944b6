/* ===== COMPLETE REDESIGN - CLEAN MODERN LAYOUT ===== */

/* Reset and Base Styles for Redesigned Pages */
.teams-page-header,
.schedule-page-header,
.bracket-page-header {
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    padding: 100px 0 60px;
    margin-top: 70px;
    position: relative;
    overflow: hidden;
}

.teams-page-header::before,
.schedule-page-header::before,
.bracket-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 70, 85, 0.1) 0%, transparent 70%);
}

.header-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.page-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #FF4655, #FFFFFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-description {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* ===== TEAMS PAGE SPECIFIC ===== */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.quick-stat {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 70, 85, 0.2);
    text-align: center;
    transition: transform 0.3s ease;
}

.quick-stat:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.quick-stat .stat-number {
    display: block;
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-red);
    margin-bottom: 0.5rem;
}

.quick-stat .stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== CONTROLS SECTION ===== */
.teams-controls,
.schedule-filters,
.bracket-nav {
    background: var(--card-bg);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.controls-wrapper,
.filters-wrapper,
.nav-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.filter-section,
.sort-section,
.view-section,
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 150px;
}

.control-title,
.filter-group label {
    color: var(--primary-red);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-buttons,
.view-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn,
.view-btn,
.nav-tab {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-btn:hover,
.view-btn:hover,
.nav-tab:hover,
.filter-btn.active,
.view-btn.active,
.nav-tab.active {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: white;
    transform: translateY(-2px);
}

.sort-select,
.filter-select {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 150px;
}

.sort-select:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-red);
    box-shadow: 0 0 0 2px rgba(255, 70, 85, 0.2);
}

/* ===== MAIN CONTENT AREAS ===== */
.teams-display,
.tab-content-container,
.bracket-section {
    background: var(--dark-bg);
    padding: 3rem 0;
    min-height: 60vh;
}

.teams-container,
.schedule-overview,
.bracket-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ===== TEAMS GRID REDESIGN ===== */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.team-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-red), #FF6B7A);
}

.team-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-red);
    box-shadow: 0 10px 30px rgba(255, 70, 85, 0.2);
}

.team-card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.team-card-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-red), #FF6B7A);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: white;
}

.team-card-info {
    flex: 1;
}

.team-card-name {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.team-card-record {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.team-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-top: 0.5rem;
    display: inline-block;
}

.team-status.active {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

.team-status.eliminated {
    background: rgba(170, 171, 173, 0.2);
    color: var(--text-secondary);
    border: 1px solid var(--text-secondary);
}

.team-players {
    margin-top: 1.5rem;
}

.team-players h4 {
    color: var(--primary-red);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.players-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.player {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.player:last-child {
    border-bottom: none;
}

.player-name {
    font-weight: 500;
    color: var(--text-primary);
}

.player-role {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Role-specific colors */
.player-role[style*="color: #FF4655"] { background: rgba(255, 70, 85, 0.2); }
.player-role[style*="color: #00D4FF"] { background: rgba(0, 212, 255, 0.2); }
.player-role[style*="color: #FFCC02"] { background: rgba(255, 204, 2, 0.2); }
.player-role[style*="color: #00F5A0"] { background: rgba(0, 245, 160, 0.2); }
.player-role[style*="color: #AAABAD"] { background: rgba(170, 171, 173, 0.2); }

/* ===== STATISTICS SECTION ===== */
.detailed-stats,
.tournament-summary {
    background: var(--card-bg);
    padding: 4rem 0;
}

.stats-container,
.summary-grid {
    max-width: 1200px;
    margin: 2rem auto 0;
    padding: 0 2rem;
}

.stats-row,
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.stat-card,
.summary-card {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before,
.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-red);
}

.stat-card:hover,
.summary-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
    box-shadow: 0 10px 30px rgba(255, 70, 85, 0.2);
}

.stat-icon,
.summary-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 70, 85, 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-content,
.summary-content {
    flex: 1;
}

.stat-content h3,
.summary-content h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-content .stat-value,
.summary-content p {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-red);
}

/* ===== SCHEDULE PAGE SPECIFIC ===== */
.tournament-progress {
    max-width: 500px;
    margin: 2rem auto 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.progress-percentage {
    color: var(--primary-red);
    font-weight: 700;
    font-size: 1.2rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-red), #FF6B7A);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.schedule-nav {
    background: var(--dark-bg);
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.tab-content {
    display: none;
    padding: 3rem 0;
}

.tab-content.active {
    display: block;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-secondary,
.btn-primary {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-secondary {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-secondary:hover {
    background: var(--border-color);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.btn-primary {
    background: var(--primary-red);
    color: white;
}

.btn-primary:hover {
    background: #E63946;
    transform: translateY(-2px);
}

/* ===== SCHEDULE TABLE ===== */
.schedule-table-wrapper {
    background: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    margin-top: 2rem;
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
}

.schedule-table th {
    background: var(--dark-bg);
    color: var(--primary-red);
    padding: 1.5rem 1rem;
    text-align: left;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.schedule-table td {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    vertical-align: middle;
}

.schedule-table tr:hover {
    background: rgba(255, 70, 85, 0.05);
}

.schedule-table .teams {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.schedule-table .team {
    font-weight: 500;
}

.schedule-table .team.winner {
    color: var(--success-green);
}

.schedule-table .vs {
    color: var(--text-secondary);
    font-weight: 400;
}

.match-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.match-status.upcoming {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border: 1px solid var(--warning-yellow);
}

.match-status.live {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border: 1px solid var(--primary-red);
    animation: pulse 2s infinite;
}

.match-status.completed {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

/* ===== BRACKET PAGE SPECIFIC ===== */
.bracket-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    max-width: 600px;
    margin: 2rem auto 0;
}

.status-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 70, 85, 0.2);
    text-align: center;
    transition: transform 0.3s ease;
}

.status-item:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.status-label {
    display: block;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-value {
    display: block;
    color: var(--primary-red);
    font-size: 1.8rem;
    font-weight: 700;
}

.bracket-legend {
    flex: 1;
}

.bracket-legend h3 {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.legend-items {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.legend-color.completed {
    background: var(--success-green);
}

.legend-color.upcoming {
    background: var(--warning-yellow);
}

.legend-color.winner {
    background: var(--primary-red);
}

.legend-color.eliminated {
    background: var(--text-secondary);
}

.bracket-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.bracket-actions button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* ===== BRACKET VISUALIZATION ===== */
.bracket-container {
    overflow-x: auto;
    padding: 2rem 0;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin-top: 2rem;
}

.bracket-scroll {
    min-width: 1000px;
    padding: 2rem;
}

.bracket {
    display: flex;
    gap: 4rem;
    justify-content: center;
    align-items: flex-start;
}

.bracket-round {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    min-width: 200px;
}

.bracket-round-title {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-red);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bracket-match {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.bracket-match:hover {
    border-color: var(--primary-red);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.2);
}

.bracket-match.completed {
    border-color: var(--success-green);
}

.bracket-match.upcoming {
    border-color: var(--warning-yellow);
}

.bracket-team {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.bracket-team:last-child {
    border-bottom: none;
}

.bracket-team.winner {
    background: rgba(0, 245, 160, 0.1);
    color: var(--success-green);
    font-weight: 600;
}

.bracket-team-name {
    font-weight: 500;
    font-size: 0.9rem;
}

.bracket-team-score {
    font-weight: 700;
    color: var(--primary-red);
    font-size: 1rem;
}

.bracket-match-info {
    text-align: center;
    margin-top: 0.5rem;
}

/* ===== CHAMPIONSHIP SECTION ===== */
.championship-spotlight {
    background: var(--card-bg);
    padding: 4rem 0;
}

.championship-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.championship-card {
    background: var(--dark-bg);
    border: 2px solid var(--primary-red);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.championship-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 70, 85, 0.05) 0%, transparent 70%);
}

.championship-card > * {
    position: relative;
    z-index: 2;
}

.champion-team {
    margin: 2rem 0;
}

.team-logo.large {
    width: 100px;
    height: 100px;
    font-size: 3rem;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--primary-red), #FF6B7A);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.final-matchup {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    margin: 2rem 0;
}

.finalist {
    text-align: center;
}

.vs-final {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-red);
}

.prize-info ul {
    list-style: none;
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

.prize-info li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
}

.prize-info li:last-child {
    border-bottom: none;
}

/* ===== TIMELINE STYLES ===== */
.timeline-wrapper {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.timeline {
    position: relative;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    bottom: -3rem;
    width: 2px;
    background: var(--border-color);
}

.timeline-item:last-child::before {
    display: none;
}

.timeline-marker {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    border: 3px solid var(--card-bg);
    position: relative;
    z-index: 2;
    margin-right: 2rem;
    flex-shrink: 0;
}

.timeline-item.completed .timeline-marker {
    background: var(--success-green);
}

.timeline-item.active .timeline-marker {
    background: var(--primary-red);
    animation: pulse 2s infinite;
}

.timeline-content {
    flex: 1;
    background: var(--dark-bg);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.timeline-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.timeline-title {
    color: var(--primary-red);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.timeline-description {
    color: var(--text-secondary);
}

/* ===== DETAILED RESULTS ===== */
.detailed-results {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.matches-detailed {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 1024px) {
    .controls-wrapper,
    .filters-wrapper,
    .nav-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 1.5rem;
    }

    .filter-section,
    .sort-section,
    .view-section,
    .filter-group {
        min-width: auto;
    }

    .bracket {
        gap: 2rem;
    }

    .bracket-round {
        min-width: 180px;
    }
}

@media screen and (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }

    .page-description {
        font-size: 1.1rem;
    }

    .quick-stats,
    .bracket-status {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .teams-grid {
        grid-template-columns: 1fr;
    }

    .stats-row,
    .summary-grid {
        grid-template-columns: 1fr;
    }

    .nav-tabs {
        flex-direction: column;
        gap: 0;
    }

    .nav-tab {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .filter-buttons,
    .view-buttons {
        justify-content: center;
    }

    .filter-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .bracket-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .legend-items {
        justify-content: center;
    }

    .final-matchup {
        flex-direction: column;
        gap: 1rem;
    }

    .vs-final {
        font-size: 1.5rem;
    }

    .bracket-container {
        padding: 1rem 0;
    }

    .bracket-scroll {
        padding: 1rem;
    }

    .bracket {
        gap: 1.5rem;
    }

    .bracket-round {
        min-width: 150px;
    }

    .schedule-table th,
    .schedule-table td {
        padding: 1rem 0.5rem;
        font-size: 0.9rem;
    }

    .matches-detailed {
        grid-template-columns: 1fr;
    }

    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .timeline-marker {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .timeline-item::before {
        display: none;
    }
}

@media screen and (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }

    .quick-stats,
    .bracket-status {
        grid-template-columns: 1fr;
    }

    .team-card,
    .stat-card,
    .summary-card {
        padding: 1.5rem;
    }

    .stat-icon,
    .summary-icon {
        width: 50px;
        height: 50px;
        font-size: 2rem;
    }

    .championship-card {
        padding: 2rem;
    }

    .team-logo.large {
        width: 80px;
        height: 80px;
        font-size: 2.5rem;
    }

    .bracket-round {
        min-width: 120px;
    }

    .bracket-team-name {
        font-size: 0.8rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.section-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-red);
}

.no-matches,
.no-teams {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.no-matches h3,
.no-teams h3 {
    color: var(--primary-red);
    margin-bottom: 1rem;
}

/* ===== ANIMATIONS ===== */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
