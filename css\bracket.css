/* ===== BRACKET PAGE STYLES ===== */

/* Reset existing styles */
.bracket-page-header,
.bracket-nav,
.bracket-section,
.championship-spotlight,
.tournament-summary {
    all: unset;
    display: block;
}

/* Simple Header */
.bracket-header {
    background: #0F1419;
    padding: 100px 0 40px;
    margin-top: 70px;
    text-align: center;
}

.bracket-title {
    font-size: 2.5rem;
    color: #FFFFFF;
    margin-bottom: 10px;
    font-weight: 600;
}

.bracket-subtitle {
    font-size: 1.1rem;
    color: #AAABAD;
}

/* Main Bracket Section */
.main-bracket {
    background: #0F1419;
    padding: 60px 0;
}

.main-bracket .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Round Layout */
.bracket-round {
    margin-bottom: 60px;
}

.round-title {
    font-size: 1.8rem;
    color: #FF4655;
    text-align: center;
    margin-bottom: 40px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.matches-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Large Match Cards */
.match-card {
    background: #1E2328;
    border: 2px solid #3C3C41;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 160px;
}

.match-card:hover {
    border-color: #FF4655;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 70, 85, 0.3);
}

.match-card.grand-final {
    border: 3px solid #FFD700;
    background: linear-gradient(135deg, #1E2328 0%, #2A2D32 100%);
}

.match-card.grand-final:hover {
    border-color: #FFD700;
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
}

/* Match Header */
.match-header {
    background: #0F1419;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #3C3C41;
}

.match-label {
    font-size: 1.1rem;
    font-weight: 700;
    color: #FFFFFF;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.match-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.match-status.completed {
    background: rgba(0, 245, 160, 0.2);
    color: #00F5A0;
    border: 1px solid #00F5A0;
}

.match-status.upcoming {
    background: rgba(255, 204, 2, 0.2);
    color: #FFCC02;
    border: 1px solid #FFCC02;
}

.match-status.live {
    background: rgba(255, 70, 85, 0.2);
    color: #FF4655;
    border: 1px solid #FF4655;
    animation: pulse 2s infinite;
}

/* Team Rows */
.team-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #3C3C41;
    transition: all 0.3s ease;
}

.team-row:last-child {
    border-bottom: none;
}

.team-row.winner {
    background: rgba(0, 245, 160, 0.1);
    border-left: 4px solid #00F5A0;
}

.team-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.team-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #FF4655;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.3rem;
    color: white;
    flex-shrink: 0;
}

.team-row.winner .team-logo {
    background: #00F5A0;
    color: #0F1419;
}

.team-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #FFFFFF;
}

.team-row.winner .team-name {
    color: #00F5A0;
}

.team-score {
    font-size: 2rem;
    font-weight: 700;
    color: #FF4655;
    min-width: 40px;
    text-align: center;
}

.team-row.winner .team-score {
    color: #00F5A0;
}

/* Championship Section */
.championship-section {
    background: #1E2328;
    padding: 80px 0;
}

.champion-card {
    background: #0F1419;
    border: 3px solid #FFD700;
    border-radius: 16px;
    padding: 50px;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0 20px 50px rgba(255, 215, 0, 0.2);
}

.champion-card h2 {
    font-size: 2.2rem;
    color: #FFD700;
    margin-bottom: 30px;
    font-weight: 700;
}

.champion-info {
    margin-bottom: 40px;
}

.champion-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: #FFD700;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 2.5rem;
    color: #0F1419;
    margin: 0 auto 20px;
}

.champion-info h3 {
    font-size: 1.8rem;
    color: #FFFFFF;
    margin-bottom: 10px;
    font-weight: 600;
}

.champion-info p {
    font-size: 1.1rem;
    color: #AAABAD;
}

.prize-pool h4 {
    font-size: 1.3rem;
    color: #FF4655;
    margin-bottom: 20px;
    font-weight: 600;
}

.prizes {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.prize {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 1rem;
    color: #FFFFFF;
    border-left: 4px solid #FF4655;
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
    .matches-container {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 20px;
    }
    
    .match-card {
        min-height: 140px;
    }
    
    .team-row {
        padding: 15px;
    }
    
    .team-logo {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
    
    .team-name {
        font-size: 1.1rem;
    }
    
    .team-score {
        font-size: 1.8rem;
    }
}

@media screen and (max-width: 768px) {
    .bracket-title {
        font-size: 2rem;
    }
    
    .round-title {
        font-size: 1.5rem;
        margin-bottom: 30px;
    }
    
    .matches-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .match-card {
        min-height: 120px;
    }
    
    .match-header {
        padding: 12px 15px;
    }
    
    .match-label {
        font-size: 1rem;
    }
    
    .team-row {
        padding: 15px;
    }
    
    .team-logo {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .team-name {
        font-size: 1rem;
    }
    
    .team-score {
        font-size: 1.5rem;
    }
    
    .champion-card {
        padding: 30px 20px;
    }
    
    .champion-card h2 {
        font-size: 1.8rem;
    }
    
    .champion-logo {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
}

@media screen and (max-width: 480px) {
    .bracket-title {
        font-size: 1.8rem;
    }
    
    .round-title {
        font-size: 1.3rem;
        margin-bottom: 25px;
    }
    
    .match-card {
        min-height: 100px;
    }
    
    .match-header {
        padding: 10px 15px;
    }
    
    .match-label {
        font-size: 0.9rem;
    }
    
    .team-row {
        padding: 12px 15px;
    }
    
    .team-info {
        gap: 10px;
    }
    
    .team-logo {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .team-name {
        font-size: 0.9rem;
    }
    
    .team-score {
        font-size: 1.3rem;
    }
    
    .champion-card {
        padding: 25px 15px;
    }
    
    .champion-card h2 {
        font-size: 1.5rem;
    }
    
    .champion-logo {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }
    
    .champion-info h3 {
        font-size: 1.4rem;
    }
}
