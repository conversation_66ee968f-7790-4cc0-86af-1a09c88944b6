/* ===== SCHEDULE PAGE STYLES ===== */

/* Reset existing styles */
.schedule-page-header,
.schedule-nav,
.schedule-filters,
.tab-content-container {
    all: unset;
    display: block;
}

/* Page Header */
.schedule-page-header {
    background: #0F1419;
    padding: 100px 0 40px;
    margin-top: 70px;
    text-align: center;
}

.header-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.page-title {
    font-size: 2.5rem;
    color: #FFFFFF;
    margin-bottom: 10px;
    font-weight: 600;
}

.page-description {
    font-size: 1.1rem;
    color: #AAABAD;
    margin-bottom: 30px;
}

/* Tournament Progress */
.tournament-progress {
    max-width: 400px;
    margin: 20px auto 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.progress-label {
    color: #AAABAD;
    font-size: 0.9rem;
}

.progress-percentage {
    color: #FF4655;
    font-weight: 600;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #3C3C41;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #FF4655;
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Tab Navigation */
.schedule-nav {
    background: #0F1419;
    padding: 15px 0;
    border-bottom: 1px solid #3C3C41;
}

.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-tab {
    background: transparent;
    border: 1px solid #3C3C41;
    color: #AAABAD;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.nav-tab:hover,
.nav-tab.active {
    background: #FF4655;
    border-color: #FF4655;
    color: white;
}

/* Filters Section */
.schedule-filters {
    background: #1E2328;
    padding: 20px 0;
    border-bottom: 1px solid #3C3C41;
}

.filters-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    color: #FF4655;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.filter-select {
    background: #0F1419;
    border: 1px solid #3C3C41;
    color: #FFFFFF;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

.btn-secondary,
.btn-primary {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.btn-secondary {
    background: transparent;
    border: 1px solid #3C3C41;
    color: #AAABAD;
}

.btn-secondary:hover {
    background: #3C3C41;
    color: white;
}

.btn-primary {
    background: #FF4655;
    color: white;
}

.btn-primary:hover {
    background: #E63946;
}

/* Tab Content */
.tab-content-container {
    background: #0F1419;
    padding: 40px 0;
    min-height: 500px;
}

.tab-content {
    display: none;
    padding: 40px 0;
}

.tab-content.active {
    display: block;
}

.schedule-overview {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Schedule Table */
.schedule-table-wrapper {
    background: #1E2328;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #3C3C41;
    margin-top: 20px;
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
}

.schedule-table th {
    background: #0F1419;
    color: #FF4655;
    padding: 15px 10px;
    text-align: left;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    border-bottom: 1px solid #3C3C41;
}

.schedule-table td {
    padding: 15px 10px;
    border-bottom: 1px solid #3C3C41;
    color: #FFFFFF;
    font-size: 0.9rem;
}

.schedule-table tr:hover {
    background: rgba(255, 70, 85, 0.05);
}

.schedule-table .teams {
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-table .team {
    font-weight: 500;
}

.schedule-table .team.winner {
    color: #00F5A0;
}

.schedule-table .vs {
    color: #AAABAD;
}

.match-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.match-status.upcoming {
    background: rgba(255, 204, 2, 0.2);
    color: #FFCC02;
}

.match-status.live {
    background: rgba(255, 70, 85, 0.2);
    color: #FF4655;
}

.match-status.completed {
    background: rgba(0, 245, 160, 0.2);
    color: #00F5A0;
}

/* Timeline Styles */
.timeline-wrapper {
    max-width: 600px;
    margin: 20px auto;
    padding: 0 20px;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #1E2328;
    border-radius: 6px;
    border-left: 4px solid #3C3C41;
}

.timeline-item.completed {
    border-left-color: #00F5A0;
}

.timeline-item.active {
    border-left-color: #FF4655;
}

.timeline-marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #3C3C41;
    margin-right: 15px;
    flex-shrink: 0;
}

.timeline-item.completed .timeline-marker {
    background: #00F5A0;
}

.timeline-item.active .timeline-marker {
    background: #FF4655;
}

.timeline-content {
    flex: 1;
}

.timeline-date {
    color: #AAABAD;
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.timeline-title {
    color: #FFFFFF;
    font-size: 1rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-description {
    color: #AAABAD;
    font-size: 0.85rem;
}

/* Section Titles */
.section-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
    color: #FFFFFF;
    font-weight: 600;
    text-transform: uppercase;
}

.section-title::after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #FF4655;
    margin: 10px auto 0;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .filters-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .filter-group {
        align-items: center;
        text-align: center;
    }

    .nav-tabs {
        flex-direction: column;
        gap: 10px;
    }

    .nav-tab {
        width: 100%;
        text-align: center;
    }

    .filter-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .schedule-table th,
    .schedule-table td {
        padding: 10px 8px;
        font-size: 0.8rem;
    }

    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .timeline-marker {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

@media screen and (max-width: 480px) {
    .page-title {
        font-size: 1.8rem;
    }

    .schedule-table th,
    .schedule-table td {
        padding: 8px 5px;
        font-size: 0.75rem;
    }
}
