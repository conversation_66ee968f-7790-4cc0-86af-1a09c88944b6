/* ===== CLEAN MINIMAL REDESIGN ===== */

/* Reset and override existing styles */
.teams-page-header,
.schedule-page-header,
.bracket-page-header,
.teams-controls,
.schedule-filters,
.bracket-nav,
.teams-display,
.tab-content-container,
.bracket-section,
.detailed-stats,
.tournament-summary,
.championship-spotlight {
    all: unset;
    display: block;
}

/* ===== CLEAN PAGE HEADERS ===== */
.teams-page-header,
.schedule-page-header,
.bracket-page-header {
    background: #0F1419;
    padding: 100px 0 40px;
    margin-top: 70px;
    text-align: center;
}

.header-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.page-title {
    font-size: 2.5rem;
    color: #FFFFFF;
    margin-bottom: 10px;
    font-weight: 600;
}

.page-description {
    font-size: 1.1rem;
    color: #AAABAD;
    margin-bottom: 30px;
}

/* ===== SIMPLE STATS ===== */
.quick-stats,
.bracket-status {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
}

.quick-stat,
.status-item {
    text-align: center;
    padding: 20px;
    background: #1E2328;
    border-radius: 8px;
    min-width: 120px;
}

.stat-number,
.status-value {
    display: block;
    font-size: 1.8rem;
    color: #FF4655;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-label,
.status-label {
    font-size: 0.9rem;
    color: #AAABAD;
    text-transform: uppercase;
}

/* ===== CLEAN CONTROLS ===== */
.teams-controls,
.schedule-filters,
.bracket-nav {
    background: #1E2328;
    padding: 20px 0;
    border-bottom: 1px solid #3C3C41;
}

.controls-wrapper,
.filters-wrapper,
.nav-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-section,
.sort-section,
.view-section,
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-title,
.filter-group label {
    color: #FF4655;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.filter-buttons,
.view-buttons {
    display: flex;
    gap: 8px;
}

.filter-btn,
.view-btn,
.nav-tab {
    background: transparent;
    border: 1px solid #3C3C41;
    color: #AAABAD;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.filter-btn:hover,
.view-btn:hover,
.nav-tab:hover,
.filter-btn.active,
.view-btn.active,
.nav-tab.active {
    background: #FF4655;
    border-color: #FF4655;
    color: white;
}

.sort-select,
.filter-select {
    background: #0F1419;
    border: 1px solid #3C3C41;
    color: #FFFFFF;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

.btn-secondary,
.btn-primary {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.btn-secondary {
    background: transparent;
    border: 1px solid #3C3C41;
    color: #AAABAD;
}

.btn-secondary:hover {
    background: #3C3C41;
    color: white;
}

.btn-primary {
    background: #FF4655;
    color: white;
}

.btn-primary:hover {
    background: #E63946;
}

/* ===== MAIN CONTENT AREAS ===== */
.teams-display,
.tab-content-container,
.bracket-section {
    background: #0F1419;
    padding: 40px 0;
    min-height: 500px;
}

.teams-container,
.schedule-overview,
.bracket-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== SIMPLE TEAMS GRID ===== */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.team-card {
    background: #1E2328;
    border: 1px solid #3C3C41;
    border-radius: 8px;
    padding: 20px;
    transition: border-color 0.2s ease;
}

.team-card:hover {
    border-color: #FF4655;
}

.team-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #3C3C41;
}

.team-card-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #FF4655;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
    color: white;
}

.team-card-info {
    flex: 1;
}

.team-card-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #FFFFFF;
    margin-bottom: 5px;
}

.team-card-record {
    color: #AAABAD;
    font-size: 0.85rem;
}

.team-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 5px;
    display: inline-block;
}

.team-status.active {
    background: rgba(0, 245, 160, 0.2);
    color: #00F5A0;
}

.team-status.eliminated {
    background: rgba(170, 171, 173, 0.2);
    color: #AAABAD;
}

.team-players h4 {
    color: #FF4655;
    font-size: 0.85rem;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.players-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.player {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.player-name {
    color: #FFFFFF;
    font-size: 0.9rem;
}

.player-role {
    color: #AAABAD;
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

/* ===== SCHEDULE TAB NAVIGATION ===== */
.schedule-nav {
    background: #0F1419;
    padding: 15px 0;
    border-bottom: 1px solid #3C3C41;
}

.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.tab-content {
    display: none;
    padding: 40px 0;
}

.tab-content.active {
    display: block;
}

/* ===== SIMPLE PROGRESS BAR ===== */
.tournament-progress {
    max-width: 400px;
    margin: 20px auto 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.progress-label {
    color: #AAABAD;
    font-size: 0.9rem;
}

.progress-percentage {
    color: #FF4655;
    font-weight: 600;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #3C3C41;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #FF4655;
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* ===== CLEAN SCHEDULE TABLE ===== */
.schedule-table-wrapper {
    background: #1E2328;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #3C3C41;
    margin-top: 20px;
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
}

.schedule-table th {
    background: #0F1419;
    color: #FF4655;
    padding: 15px 10px;
    text-align: left;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    border-bottom: 1px solid #3C3C41;
}

.schedule-table td {
    padding: 15px 10px;
    border-bottom: 1px solid #3C3C41;
    color: #FFFFFF;
    font-size: 0.9rem;
}

.schedule-table tr:hover {
    background: rgba(255, 70, 85, 0.05);
}

.schedule-table .teams {
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-table .team {
    font-weight: 500;
}

.schedule-table .team.winner {
    color: #00F5A0;
}

.schedule-table .vs {
    color: #AAABAD;
}

.match-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.match-status.upcoming {
    background: rgba(255, 204, 2, 0.2);
    color: #FFCC02;
}

.match-status.live {
    background: rgba(255, 70, 85, 0.2);
    color: #FF4655;
}

.match-status.completed {
    background: rgba(0, 245, 160, 0.2);
    color: #00F5A0;
}

/* ===== SIMPLE BRACKET ===== */
.bracket-container {
    overflow-x: auto;
    padding: 20px 0;
    background: #1E2328;
    border-radius: 8px;
    border: 1px solid #3C3C41;
    margin-top: 20px;
}

.bracket-scroll {
    min-width: 800px;
    padding: 20px;
}

.bracket {
    display: flex;
    gap: 40px;
    justify-content: center;
    align-items: flex-start;
}

.bracket-round {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 180px;
}

.bracket-round-title {
    text-align: center;
    font-size: 1rem;
    font-weight: 600;
    color: #FF4655;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.bracket-match {
    background: #0F1419;
    border: 1px solid #3C3C41;
    border-radius: 6px;
    padding: 10px;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.bracket-match:hover {
    border-color: #FF4655;
}

.bracket-match.completed {
    border-color: #00F5A0;
}

.bracket-match.upcoming {
    border-color: #FFCC02;
}

.bracket-team {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #3C3C41;
}

.bracket-team:last-child {
    border-bottom: none;
}

.bracket-team.winner {
    background: rgba(0, 245, 160, 0.1);
    color: #00F5A0;
    font-weight: 600;
}

.bracket-team-name {
    font-size: 0.85rem;
    color: #FFFFFF;
}

.bracket-team-score {
    font-weight: 600;
    color: #FF4655;
}

.bracket-match-info {
    text-align: center;
    margin-top: 8px;
}

/* ===== SIMPLE LEGEND ===== */
.bracket-legend {
    flex: 1;
}

.bracket-legend h3 {
    color: #AAABAD;
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.legend-items {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-color.completed {
    background: #00F5A0;
}

.legend-color.upcoming {
    background: #FFCC02;
}

.legend-color.winner {
    background: #FF4655;
}

.legend-color.eliminated {
    background: #AAABAD;
}

/* ===== SIMPLE CHAMPIONSHIP ===== */
.championship-spotlight {
    background: #1E2328;
    padding: 40px 0;
}

.championship-wrapper {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
}

.championship-card {
    background: #0F1419;
    border: 1px solid #FF4655;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
}

.champion-team {
    margin: 20px 0;
}

.team-logo.large {
    width: 80px;
    height: 80px;
    font-size: 2rem;
    margin: 0 auto 15px;
    background: #FF4655;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.final-matchup {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    margin: 20px 0;
}

.finalist {
    text-align: center;
}

.vs-final {
    font-size: 1.5rem;
    font-weight: 600;
    color: #FF4655;
}

/* ===== SIMPLE STATS ===== */
.detailed-stats,
.tournament-summary {
    background: #1E2328;
    padding: 40px 0;
}

.stats-container,
.summary-grid {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.stats-row,
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card,
.summary-card {
    background: #0F1419;
    border: 1px solid #3C3C41;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon,
.summary-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 70, 85, 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-content h3,
.summary-content h3 {
    color: #AAABAD;
    font-size: 0.85rem;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.stat-content .stat-value,
.summary-content p {
    font-size: 1.3rem;
    font-weight: 600;
    color: #FF4655;
}

/* ===== SIMPLE TIMELINE ===== */
.timeline-wrapper {
    max-width: 600px;
    margin: 20px auto;
    padding: 0 20px;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #1E2328;
    border-radius: 6px;
    border-left: 4px solid #3C3C41;
}

.timeline-item.completed {
    border-left-color: #00F5A0;
}

.timeline-item.active {
    border-left-color: #FF4655;
}

.timeline-marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #3C3C41;
    margin-right: 15px;
    flex-shrink: 0;
}

.timeline-item.completed .timeline-marker {
    background: #00F5A0;
}

.timeline-item.active .timeline-marker {
    background: #FF4655;
}

.timeline-content {
    flex: 1;
}

.timeline-date {
    color: #AAABAD;
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.timeline-title {
    color: #FFFFFF;
    font-size: 1rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-description {
    color: #AAABAD;
    font-size: 0.85rem;
}

/* ===== SECTION TITLES ===== */
.section-title {
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
    color: #FFFFFF;
    font-weight: 600;
    text-transform: uppercase;
}

.section-title::after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #FF4655;
    margin: 10px auto 0;
}

/* ===== UTILITY CLASSES ===== */
.no-matches,
.no-teams {
    text-align: center;
    padding: 40px 20px;
    color: #AAABAD;
}

.no-matches h3,
.no-teams h3 {
    color: #FF4655;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .quick-stats,
    .bracket-status {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .quick-stat,
    .status-item {
        min-width: 200px;
    }

    .controls-wrapper,
    .filters-wrapper,
    .nav-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .filter-section,
    .sort-section,
    .view-section,
    .filter-group {
        align-items: center;
        text-align: center;
    }

    .teams-grid {
        grid-template-columns: 1fr;
    }

    .stats-row,
    .summary-grid {
        grid-template-columns: 1fr;
    }

    .nav-tabs {
        flex-direction: column;
        gap: 10px;
    }

    .nav-tab {
        width: 100%;
        text-align: center;
    }

    .filter-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .bracket-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .legend-items {
        justify-content: center;
    }

    .final-matchup {
        flex-direction: column;
        gap: 15px;
    }

    .vs-final {
        font-size: 1.2rem;
    }

    .bracket {
        gap: 20px;
    }

    .bracket-round {
        min-width: 150px;
    }

    .schedule-table th,
    .schedule-table td {
        padding: 10px 8px;
        font-size: 0.8rem;
    }

    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .timeline-marker {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

@media screen and (max-width: 480px) {
    .page-title {
        font-size: 1.8rem;
    }

    .quick-stat,
    .status-item {
        min-width: 150px;
        padding: 15px;
    }

    .team-card,
    .stat-card,
    .summary-card {
        padding: 15px;
    }

    .team-card-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .team-card-logo {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stat-icon,
    .summary-icon {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }

    .championship-card {
        padding: 20px;
    }

    .team-logo.large {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .bracket-round {
        min-width: 120px;
    }

    .bracket-team-name {
        font-size: 0.75rem;
    }

    .schedule-table th,
    .schedule-table td {
        padding: 8px 5px;
        font-size: 0.75rem;
    }
}

/* ===== OVERRIDE EXISTING STYLES ===== */
.teams-page-header *,
.schedule-page-header *,
.bracket-page-header *,
.teams-controls *,
.schedule-filters *,
.bracket-nav *,
.teams-display *,
.tab-content-container *,
.bracket-section *,
.detailed-stats *,
.tournament-summary *,
.championship-spotlight * {
    box-sizing: border-box;
}

/* Remove any existing animations or transitions that might conflict */
.team-card,
.bracket-match,
.schedule-table tr,
.stat-card,
.summary-card {
    transform: none !important;
    box-shadow: none !important;
}

.team-card:hover,
.bracket-match:hover,
.stat-card:hover,
.summary-card:hover {
    transform: none !important;
    box-shadow: none !important;
}
