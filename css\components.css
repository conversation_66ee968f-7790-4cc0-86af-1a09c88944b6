/* ===== MATCH CARDS ===== */
.match-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

.match-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-red);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.match-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.match-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.match-status.upcoming {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border: 1px solid var(--warning-yellow);
}

.match-status.live {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border: 1px solid var(--primary-red);
    animation: pulse 2s infinite;
}

.match-status.completed {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.team {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.team.winner {
    color: var(--success-green);
}

.team-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--primary-red);
}

.team-name {
    font-weight: 600;
    font-size: 1.1rem;
}

.vs-divider {
    color: var(--text-secondary);
    font-weight: 700;
    font-size: 1.2rem;
    margin: 0 1rem;
}

.match-score {
    text-align: center;
    margin-bottom: 1rem;
}

.score {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-red);
}

.match-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.match-map {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.match-round {
    color: var(--primary-red);
    font-weight: 600;
    font-size: 0.9rem;
}

/* ===== TEAM CARDS ===== */
.team-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.team-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.team-card-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 2rem;
    color: var(--primary-red);
    margin: 0 auto 1rem;
}

.team-card-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.team-card-record {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.team-players {
    list-style: none;
}

.player {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.player:last-child {
    border-bottom: none;
}

.player-name {
    font-weight: 500;
}

.player-role {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== BRACKET STYLES ===== */
.bracket-container {
    overflow-x: auto;
    padding: 2rem 0;
}

.bracket {
    display: flex;
    gap: 4rem;
    min-width: 800px;
    justify-content: center;
}

.bracket-round {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    min-width: 200px;
}

.bracket-round-title {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-red);
    margin-bottom: 1rem;
}

.bracket-match {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    position: relative;
}

.bracket-team {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.bracket-team:last-child {
    border-bottom: none;
}

.bracket-team.winner {
    background: rgba(0, 245, 160, 0.1);
    color: var(--success-green);
}

.bracket-team-name {
    font-weight: 500;
}

.bracket-team-score {
    font-weight: 700;
    color: var(--primary-red);
}

/* ===== SCHEDULE TABLE ===== */
.schedule-table {
    background: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.schedule-table table {
    width: 100%;
    border-collapse: collapse;
}

.schedule-table th,
.schedule-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.schedule-table th {
    background: var(--dark-bg);
    color: var(--primary-red);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.schedule-table tr:hover {
    background: rgba(255, 70, 85, 0.05);
}

.schedule-table .status-cell {
    text-align: center;
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.text-red {
    color: var(--primary-red);
}

.text-secondary {
    color: var(--text-secondary);
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* ===== PAGE HEADERS ===== */
.page-header {
    background: linear-gradient(135deg, #0F1419 0%, #1E2328 100%);
    padding: 120px 0 60px;
    text-align: center;
    margin-top: 70px;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #FF4655, #FFFFFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

/* ===== FILTERS AND CONTROLS ===== */
.team-filters,
.schedule-filters,
.bracket-controls {
    background: var(--card-bg);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.filter-controls,
.controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: white;
}

.sort-controls,
.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-select,
.filter-select {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.sort-select:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-red);
}

/* ===== TOURNAMENT STATS ===== */
.tournament-stats {
    background: var(--dark-bg);
    padding: 4rem 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-red);
    display: block;
}

/* ===== TIMELINE STYLES ===== */
.tournament-timeline {
    background: var(--card-bg);
    padding: 4rem 0;
}

.timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    bottom: -3rem;
    width: 2px;
    background: var(--border-color);
}

.timeline-item:last-child::before {
    display: none;
}

.timeline-marker {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    border: 3px solid var(--card-bg);
    position: relative;
    z-index: 2;
    margin-right: 2rem;
}

.timeline-item.completed .timeline-marker {
    background: var(--success-green);
}

.timeline-item.active .timeline-marker {
    background: var(--primary-red);
    animation: pulse 2s infinite;
}

.timeline-content {
    flex: 1;
}

.timeline-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.timeline-title {
    color: var(--primary-red);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.timeline-description {
    color: var(--text-secondary);
}

/* ===== BRACKET LEGEND ===== */
.bracket-legend {
    background: var(--dark-bg);
    padding: 2rem 0;
}

.legend-items {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.legend-color.completed {
    background: var(--success-green);
}

.legend-color.upcoming {
    background: var(--warning-yellow);
}

.legend-color.winner {
    background: var(--primary-red);
}

.legend-color.eliminated {
    background: var(--text-secondary);
}

/* ===== CHAMPIONSHIP STYLES ===== */
.championship-info {
    background: var(--card-bg);
    padding: 4rem 0;
}

.championship-details {
    background: var(--dark-bg);
    border: 2px solid var(--primary-red);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.champion-team {
    margin: 2rem 0;
}

.team-logo.large {
    width: 100px;
    height: 100px;
    font-size: 3rem;
    margin: 0 auto 1rem;
}

.final-matchup {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    margin: 2rem 0;
}

.finalist {
    text-align: center;
}

.vs-final {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-red);
}

.prize-info ul {
    list-style: none;
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

.prize-info li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 768px) {
    .match-teams {
        flex-direction: column;
        gap: 1rem;
    }

    .vs-divider {
        margin: 0;
    }

    .team {
        justify-content: center;
    }

    .bracket {
        gap: 2rem;
    }

    .bracket-round {
        min-width: 150px;
    }

    .schedule-table {
        font-size: 0.9rem;
    }

    .schedule-table th,
    .schedule-table td {
        padding: 0.75rem 0.5rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .filter-controls,
    .controls-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .filter-buttons {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .final-matchup {
        flex-direction: column;
        gap: 1rem;
    }

    .vs-final {
        font-size: 1.5rem;
    }

    .legend-items {
        flex-direction: column;
        align-items: center;
    }
}
